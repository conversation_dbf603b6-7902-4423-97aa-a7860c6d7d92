# Revise Resident Patterns CRUD for Workspaces

**Objective:** Update the CRUD operations for `ResidentPattern` to be workspace-aware, ensuring that patterns are managed within the context of the user's current workspace.

## Changes

### 1. File Structure Change

To make `workspaceId` available to the resident patterns page, the directory will be moved:

-   **From:** `src/app/(main)/deposits/settings/resident-patterns`
-   **To:** `src/app/(main)/workspaces/[workspaceId]/settings/resident-patterns`

This change will make the `workspaceId` available in the page props.

### 2. `src/actions/resident-patterns.ts`

The action file will be updated to use `workspaceId` and `memberId` for all operations, instead of `userId`.

-   **`getResidentPatterns(workspaceId: string)`:**
    -   Accepts a `workspaceId`.
    -   Verifies that the current user is a member of the specified workspace.
    -   Fetches patterns associated with the `workspaceId`.

-   **`createResidentPattern(data: { residentId: string; patterns: string; workspaceId: string })`:**
    -   Accepts a `workspaceId`.
    -   Retrieves the `memberId` for the current user in the given workspace.
    -   Creates the `ResidentPattern` with `workspaceId` and `memberId`.

-   **`updateResidentPattern(id: string, data: { residentId: string; patterns: string[]; workspaceId: string })`:**
    -   Accepts a `workspaceId`.
    -   Verifies the user's membership and permissions within the workspace.
    -   Updates the specified pattern.

-   **`deleteResidentPattern(id: string, workspaceId: string)`:**
    -   Accepts a `workspaceId`.
    -   Verifies the user's membership and permissions.
    -   Deletes the specified pattern.

### 3. `src/app/(main)/workspaces/[workspaceId]/settings/resident-patterns/page.tsx` (New location)

This page will be updated to fetch and pass the `workspaceId` to the `getResidentPatterns` action.

-   The page will get the `workspaceId` from the page `params`.
-   The `workspaceId` will be passed to `getResidentPatterns`.
-   The `workspaceId` will be passed to the `ResidentPatternList` component.

### 4. `src/app/(main)/workspaces/[workspaceId]/settings/resident-patterns/resident-pattern-list.tsx` (New location)

This component will be updated to handle the new workspace-aware actions.

-   It will need to receive the `workspaceId` as a prop.
-   It will pass the `workspaceId` to the `create`, `update`, and `delete` actions.