
import { PrismaClient } from '@prisma/client';
import { ItemType } from '@/lib/types';
import * as XLSX from 'xlsx';
import path from 'path';

const prisma = new PrismaClient();

// Mapping from display name to ItemType enum
const itemTypeMap: { [key: string]: ItemType } = {
  'A棟磁扣': ItemType.A_BUILDING_CARD,
  'B棟磁扣': ItemType.B_BUILDING_CARD,
  '全區磁扣': ItemType.ALL_AREA_CARD,
  '大門遙控器': ItemType.MAIN_GATE_REMOTE_CONTROL,
};

async function main() {
  const workspaceId = "cm5n9zkc30002mmaj3r399pc0"; // "僑星福華"
  const memberId = "cm5n9zkc30004mmajdgt3k9xr"; // Placeholder memberId

  // 1. Ensure the workspace and member exist
  const workspace = await prisma.workspace.findUnique({ where: { id: workspaceId } });
  const member = await prisma.member.findUnique({ where: { id: memberId } });

  if (!workspace || !member) {
    console.error('Workspace or member not found. Please seed the main data first.');
    return;
  }

  // 2. Ensure inventory items exist for the workspace
  const inventoryItems = [
    { itemType: ItemType.A_BUILDING_CARD, price: 200 },
    { itemType: ItemType.B_BUILDING_CARD, price: 200 },
    { itemType: ItemType.ALL_AREA_CARD, price: 200 },
    { itemType: ItemType.MAIN_GATE_REMOTE_CONTROL, price: 700 },
  ];

  for (const item of inventoryItems) {
    await prisma.inventory.upsert({
      where: { itemType_workspaceId: { itemType: item.itemType, workspaceId } },
      update: { price: item.price },
      create: {
        ...item,
        workspaceId,
        current: 0, // Initial stock is 0, will be adjusted by sales
      },
    });
  }
  console.log('✅ Inventory items checked/created successfully!');

  // 3. Read and parse the Excel file
  const filePath = path.join(process.cwd(), 'openspec/changes/revise-inventory-for-workspaces/磁扣搖控器銷售記錄.xlsx');
  const workbook = XLSX.readFile(filePath);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  const salesData = XLSX.utils.sheet_to_json(worksheet, {
    header: ["日期", "住戶", "地址", "收據編號", "收款人", "項目", "數量", "單價", "總額"],
    range: 3, // Start from row 4 (0-indexed)
  }) as any[];

  // 4. Process each sale from the Excel data
  for (const row of salesData) {
    const { 日期, 住戶, 地址, 收據編號, 收款人, 項目, 數量, 單價, 總額 } = row;

    if (!住戶 || !地址 || !項目 || !數量 || !單價) {
      console.warn('Skipping row due to missing data:', row);
      continue;
    }

    const itemType = itemTypeMap[項目];
    if (!itemType) {
      console.warn(`Skipping row due to unknown item type: "${項目}"`);
      continue;
    }

    try {
      await prisma.$transaction(async (tx: any) => {
        // Find or create resident
        const resident = await tx.resident.upsert({
          where: { name_address_workspaceId: { name: 住戶, address: 地址, workspaceId } },
          update: {},
          create: { name: 住戶, address: 地址, workspaceId },
        });

        // Get inventory item
        const inventory = await tx.inventory.findUnique({
          where: { itemType_workspaceId: { itemType, workspaceId } },
        });
        if (!inventory) {
          throw new Error(`Inventory for ${項目} not found in this workspace.`);
        }

        // Create sale and sale item
        const sale = await tx.sale.create({
          data: {
            date: new Date(Math.round((日期 - 25569) * 86400 * 1000)), // Convert Excel date
            receiptNo: 收據編號 ? String(收據編號) : null,
            recipient: 收款人 || null,
            totalAmount: 總額,
            residentId: resident.id,
            residentName: 住戶,
            address: 地址,
            workspaceId,
            saleItems: {
              create: {
                inventoryId: inventory.id,
                itemType: itemType,
                quantity: 數量,
                unitPrice: 單價,
                lineTotal: 總額,
              },
            },
          },
        });

        // Create stock movement
        await tx.stockMovement.create({
          data: {
            inventoryId: inventory.id,
            type: 'SALE',
            quantity: -數量,
            reason: `Sale to ${住戶}`,
            reference: sale.id,
            previousStock: inventory.current,
            newStock: inventory.current - 數量,
            workspaceId,
            memberId,
          },
        });

        // Update inventory stock
        await tx.inventory.update({
          where: { id: inventory.id },
          data: { current: { decrement: 數量 } },
        });
      });
      console.log(`Processed sale for ${住戶} - ${項目}`);
    } catch (error: any) {
      console.error(`Failed to process row for ${住戶}: ${error.message}`, row);
    }
  }

  console.log('✅ Inventory and sales data seeded successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding inventory data:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
