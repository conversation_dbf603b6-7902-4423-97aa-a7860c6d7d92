"use server";

import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";
import { Role } from "@/lib/types";

export async function getFeeConfigurations(workspaceId: string) {
  try {
    const { userId } = await auth();
    if (!userId) return { success: false, error: "Unauthorized" };

    const member = await prisma.member.findFirst({ where: { userId } });
    if (!member) return { success: false, error: "Unauthorized" };

    const fees = await prisma.feeConfiguration.findMany({
      where: { workspaceId },
      orderBy: { effectiveFrom: "desc" },
    });

    return { success: true, data: fees };
  } catch (e) {
    console.error("getFeeConfigurations error", e);
    return { success: false, error: "Failed to fetch fee configurations" };
  }
}

type FeePayload = {
  feeType: string;
  unitPrice: number;
  description?: string | null;
  effectiveFrom?: string;
  effectiveTo?: string | null;
  isActive?: boolean;
};

async function checkWorkspaceMembership(userId: string, workspaceId: string) {
  return prisma.member.findFirst({ where: { userId, workspaceId } });
}

export async function createFeeConfiguration(
  workspaceId: string,
  payload: FeePayload
) {
  try {
    const { userId } = await auth();
    if (!userId) return { success: false, error: "Unauthorized" };
    const member = await checkWorkspaceMembership(userId, workspaceId);
    if (!member || member.role !== Role.ADMIN) return { success: false, error: "Unauthorized" };

    const rec = await prisma.feeConfiguration.create({
      data: {
        feeType: payload.feeType as any,
        unitPrice: payload.unitPrice,
        description: payload.description,
        effectiveFrom: payload.effectiveFrom ? new Date(payload.effectiveFrom) : new Date(),
        effectiveTo: payload.effectiveTo ? new Date(payload.effectiveTo) : null,
        isActive: payload.isActive ?? true,
        workspaceId,
      },
    });

    return { success: true, data: rec };
  } catch (e) {
    console.error("createFeeConfiguration", e);
    return { success: false, error: "Failed to create fee configuration" };
  }
}

export async function updateFeeConfiguration(
  workspaceId: string,
  id: string,
  payload: FeePayload
) {
  try {
    const { userId } = await auth();
    if (!userId) return { success: false, error: "Unauthorized" };
    const member = await checkWorkspaceMembership(userId, workspaceId);
    if (!member || member.role !== Role.ADMIN) return { success: false, error: "Unauthorized" };

    const rec = await prisma.feeConfiguration.updateMany({
      where: { id, workspaceId },
      data: {
        feeType: payload.feeType as any,
        unitPrice: payload.unitPrice,
        description: payload.description,
        effectiveFrom: payload.effectiveFrom ? new Date(payload.effectiveFrom) : new Date(),
        effectiveTo: payload.effectiveTo ? new Date(payload.effectiveTo) : null,
        isActive: payload.isActive ?? true,
      },
    });

    if (rec.count === 0) return { success: false, error: "Not found" };
    const updated = await prisma.feeConfiguration.findUnique({ where: { id } });
    return { success: true, data: updated };
  } catch (e) {
    console.error("updateFeeConfiguration", e);
    return { success: false, error: "Failed to update fee configuration" };
  }
}

export async function deleteFeeConfiguration(workspaceId: string, id: string) {
  try {
    const { userId } = await auth();
    if (!userId) return { success: false, error: "Unauthorized" };
    const member = await checkWorkspaceMembership(userId, workspaceId);
    if (!member || member.role !== Role.ADMIN) return { success: false, error: "Unauthorized" };

    await prisma.feeConfiguration.deleteMany({ where: { id, workspaceId } });
    return { success: true };
  } catch (e) {
    console.error("deleteFeeConfiguration", e);
    return { success: false, error: "Failed to delete fee configuration" };
  }
}
