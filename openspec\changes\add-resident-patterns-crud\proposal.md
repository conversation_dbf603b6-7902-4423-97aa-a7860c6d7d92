## Why
Currently, the mapping of resident identifiers to transaction patterns (`RESIDENT_PATTERNS`) is hardcoded in `src/lib/google/deposits-data-process.ts`. This makes it inflexible and requires code changes to update, which is inefficient for users who need to manage these mappings directly.

## What Changes
- A new database model `ResidentPattern` will be created to store these mappings.
- The application will be updated to fetch resident patterns from the database instead of using the hardcoded map.
- A new set of UI components and APIs/server actions will be created to allow users to perform CRUD (Create, Read,Update, Delete) operations on resident patterns.

## Impact
- **Affected specs**: A new capability `resident-patterns` will be created.
- **Affected code**:
    - `prisma/schema.prisma`: A new `ResidentPattern` model will be added.
    - `src/lib/google/deposits-data-process.ts`: Will be refactored to use the new database-driven patterns.
    - New files for API/server actions and UI components for managing the patterns.
