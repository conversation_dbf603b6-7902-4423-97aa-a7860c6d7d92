"use client"
import React, { useEffect, useState, useRef } from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

export function ResidentSearch({
  workspaceId,
  value,
  onChange,
  placeholder,
}: {
  workspaceId: string
  value: string
  onChange: (val: string) => void
  placeholder?: string
}) {
  const [open, setOpen] = useState(false)
  const [query, setQuery] = useState("")
  const [options, setOptions] = useState<{ value: string; label: string }[]>([])
  const abortRef = useRef<AbortController | null>(null)

  useEffect(() => {
    if (!query || query.trim().length < 1) {
      setOptions([])
      return
    }

    const ctrl = new AbortController()
    abortRef.current?.abort()
    abortRef.current = ctrl

    const t = setTimeout(async () => {
      try {
        const res = await fetch(`/api/workspaces/${workspaceId}/residents?q=${encodeURIComponent(query)}`, { signal: ctrl.signal })
        if (!res.ok) return
        const json = await res.json()
        const opts = (json.results || []).map((r: any) => ({ value: r.residentId, label: `${r.residentId} — ${r.name || ''}` }))
        setOptions(opts)
      } catch (e) {
        if ((e as any).name === 'AbortError') return
        console.warn('resident search failed', e)
      }
    }, 200)

    return () => {
      clearTimeout(t)
      ctrl.abort()
    }
  }, [query, workspaceId])

  const handleSelect = (val: string) => {
    onChange(val)
    setOpen(false)
    setQuery('')
  }

  return (
    <div>
      <Popover open={open} onOpenChange={(o) => setOpen(o)}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn("w-full justify-between bg-green-100")}
          >
            <span className="truncate">{value || placeholder || '選擇住戶或輸入住戶編號'}</span>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput
              placeholder="搜尋住戶編號或姓名..."
              value={query}
              onValueChange={(v) => setQuery(v)}
            />
            <CommandEmpty>
              {query ? `使用 "${query}"` : '未找到住戶'}
            </CommandEmpty>
            <CommandGroup>
              {options.map((option) => (
                <CommandItem key={option.value} value={option.value} onSelect={() => handleSelect(option.value)}>
                  <Check className={cn("mr-2 h-4 w-4", value === option.value ? 'opacity-100' : 'opacity-0')} />
                  {option.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}

export default ResidentSearch
