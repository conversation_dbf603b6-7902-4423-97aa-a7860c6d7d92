"use client";

import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { useState, use } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { toast } from 'sonner';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useForm } from 'react-hook-form';

interface FeeConfig {
  id?: string;
  feeType: string;
  unitPrice: number;
  description?: string | null;
  effectiveFrom?: string | Date | null;
  effectiveTo?: string | Date | null;
  isActive?: boolean;
}

function FeeForm({
  initial,
  onClose,
  onSaved,
  workspaceId,
}: {
  initial?: Partial<FeeConfig> | null;
  onClose: () => void;
  onSaved: () => void;
  workspaceId: string;
}) {
  console.log('initial', initial);
  const { register, handleSubmit, setValue, watch } = useForm<FeeConfig>({
    defaultValues: {
      feeType: initial?.feeType || 'MANAGEMENT',
      unitPrice: initial?.unitPrice || 0,
      description: initial?.description || '',
      effectiveFrom: initial?.effectiveFrom
        ? (typeof initial!.effectiveFrom === 'string'
            ? initial!.effectiveFrom.slice(0, 10)
            : new Date(initial!.effectiveFrom).toISOString().slice(0, 10))
        : new Date().toISOString().slice(0, 10),
      effectiveTo: initial?.effectiveTo
        ? (typeof initial!.effectiveTo === 'string'
            ? initial!.effectiveTo.slice(0, 10)
            : new Date(initial!.effectiveTo).toISOString().slice(0, 10))
        : undefined,
      isActive: initial?.isActive ?? true,
    },
  });

  const queryClient = useQueryClient();
  const isActive = watch('isActive');

  const createMut = useMutation({
    mutationFn: async (payload: any) => {
      const res = await fetch(`/api/workspaces/${workspaceId}/fees`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.error || 'Failed to create fee');
      }
      return res.json();
    },
    onSuccess: (data) => {
      toast.success('Fee configuration created');
      queryClient.invalidateQueries({ queryKey: ['fees', workspaceId] });
      onSaved();
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to create fee');
    },
  });

  const updateMut = useMutation({
    mutationFn: async ({ id, payload }: { id: string; payload: any }) => {
      const res = await fetch(`/api/workspaces/${workspaceId}/fees/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.error || 'Failed to update fee');
      }
      return res.json();
    },
    onSuccess: (data) => {
      toast.success('Fee configuration updated');
      queryClient.invalidateQueries({ queryKey: ['fees', workspaceId] });
      onSaved();
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to update fee');
    },
  });

  const onSubmit = handleSubmit((vals) => {
    if (!vals.unitPrice || vals.unitPrice <= 0) {
      toast.error('Unit price must be greater than 0');
      return;
    }

    const payload = {
      feeType: vals.feeType,
      unitPrice: typeof vals.unitPrice === 'number' ? vals.unitPrice : Number(vals.unitPrice),
      description: vals.description || null,
      effectiveFrom:
        vals.effectiveFrom && typeof vals.effectiveFrom === 'string'
          ? vals.effectiveFrom
          : vals.effectiveFrom
          ? (vals.effectiveFrom as unknown as Date).toISOString().slice(0, 10)
          : null,
      effectiveTo:
        vals.effectiveTo && typeof vals.effectiveTo === 'string'
          ? vals.effectiveTo
          : vals.effectiveTo
          ? (vals.effectiveTo as unknown as Date).toISOString().slice(0, 10)
          : null,
      isActive: vals.isActive ?? true,
    };

    if (initial && initial.id) {
      updateMut.mutate({ id: initial.id, payload });
    } else {
      createMut.mutate(payload);
    }
  });

  return (
    <form onSubmit={onSubmit} className="space-y-4">
      <div>
        <Label htmlFor="feeType">Fee Type</Label>
        <Select
          onValueChange={(v) => setValue('feeType', v)}
          defaultValue={initial?.feeType || 'MANAGEMENT'}
        >
          <SelectTrigger className="w-full">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="MANAGEMENT">社區管理費</SelectItem>
            <SelectItem value="PARKING_CAR">汽車管理費</SelectItem>
            <SelectItem value="VEHICLE_BICYCLE">自行車管理費</SelectItem>
            <SelectItem value="VEHICLE_MOTORCYCLE">機車管理費</SelectItem>
            <SelectItem value="VEHICLE_HEAVY_MOTORCYCLE">重型機車管理費</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div>
        <Label htmlFor="unitPrice">Unit Price</Label>
        <Input 
          id="unitPrice"
          type="number" 
          step="1"
          min="0"
          {...register('unitPrice', { valueAsNumber: true })} 
        />
        <p className="text-sm text-muted-foreground mt-1">
          Display: NT$ {(watch('unitPrice') || 0)}
        </p>
      </div>
      
      <div>
        <Label htmlFor="description">Description (Optional)</Label>
        <Input 
          id="description"
          placeholder="e.g., Standard management fee per 坪"
          {...register('description')} 
        />
      </div>
      
      <div>
        <Label htmlFor="effectiveFrom">Effective From</Label>
        <Input 
          id="effectiveFrom"
          type="date" 
          {...register('effectiveFrom')} 
        />
      </div>
      
      <div className="flex items-center space-x-2">
        <Switch 
          id="isActive"
          checked={isActive}
          onCheckedChange={(checked) => setValue('isActive', checked)}
        />
        <Label htmlFor="isActive" className="cursor-pointer">
          Active
        </Label>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button 
          type="button" 
          variant="outline" 
          onClick={onClose}
          disabled={createMut.isPending || updateMut.isPending}
        >
          Cancel
        </Button>
        <Button 
          type="submit"
          disabled={createMut.isPending || updateMut.isPending}
        >
          {createMut.isPending || updateMut.isPending ? 'Saving...' : 'Save'}
        </Button>
      </div>
    </form>
  );
}

export default function FeesPage(props: {
  params: Promise<{ workspaceId: string }>;
}) {
  const params = use(props.params);
  const { workspaceId } = params;
  const queryClient = useQueryClient();
  const [editing, setEditing] = useState<FeeConfig | null>(null);
  const [open, setOpen] = useState(false);

  const { data, isLoading, error } = useQuery({
    queryKey: ['fees', workspaceId],
    queryFn: async () => {
      const res = await fetch(`/api/workspaces/${workspaceId}/fees`);
      if (!res.ok) {
        throw new Error('Failed to fetch fees');
      }
      const json = await res.json();
      if (!json.success) {
        throw new Error(json.error || 'Failed to fetch fees');
      }
      return json.data as FeeConfig[];
    },
  });

  const deleteMut = useMutation({
    mutationFn: async (id: string) => {
      const res = await fetch(`/api/workspaces/${workspaceId}/fees/${id}`, { 
        method: 'DELETE' 
      });
      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.error || 'Failed to delete fee');
      }
      return res.json();
    },
    onSuccess: () => {
      toast.success('Fee configuration deleted');
      queryClient.invalidateQueries({ queryKey: ['fees', workspaceId] });
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to delete fee');
    },
  });

  const handleDelete = (id: string) => {
    if (confirm('Are you sure you want to delete this fee configuration?')) {
      deleteMut.mutate(id);
    }
  };

  const handleEdit = (fee: FeeConfig) => {
    setEditing(fee);
    setOpen(true);
  };

  const handleNew = () => {
    setEditing(null);
    setOpen(true);
  };

  const getFeeTypeLabel = (type: string) => {
    switch (type) {
      case 'MANAGEMENT': return '社區管理費';
      case 'PARKING_CAR': return '汽車管理費';
      case 'VEHICLE_BICYCLE': return '自行車管理費';
      case 'VEHICLE_MOTORCYCLE': return '機車管理費';
      case 'VEHICLE_HEAVY_MOTORCYCLE': return '重型機車管理費';
      default: return type;
    }
  };

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Fee Configurations</CardTitle>
          <Button onClick={handleNew}>
            New Fee Configuration
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading && (
          <div className="text-center py-8 text-muted-foreground">
            Loading fee configurations...
          </div>
        )}

        {error && (
          <div className="text-center py-8 text-destructive">
            Error loading fees: {error.message}
          </div>
        )}

        {!isLoading && !error && data && data.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            No fee configurations found. Click "New Fee Configuration" to create one.
          </div>
        )}

        {!isLoading && !error && data && data.length > 0 && (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Type</TableHead>
                <TableHead>Unit Price</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Effective From</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.map((fee) => (
                <TableRow key={fee.id}>
                  <TableCell className="font-medium">
                    {getFeeTypeLabel(fee.feeType)}
                  </TableCell>
                  <TableCell>
                    NT$ {fee.unitPrice}
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {fee.description || '-'}
                  </TableCell>
                  <TableCell>
                    {fee.effectiveFrom 
                      ? new Date(fee.effectiveFrom).toLocaleDateString('zh-TW')
                      : '-'
                    }
                  </TableCell>
                  <TableCell>
                    <span className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                      fee.isActive 
                        ? 'bg-green-50 text-green-700 ring-1 ring-inset ring-green-600/20' 
                        : 'bg-gray-50 text-gray-600 ring-1 ring-inset ring-gray-500/10'
                    }`}>
                      {fee.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleEdit(fee)}
                      >
                        Edit
                      </Button>
                      <Button 
                        variant="destructive" 
                        size="sm"
                        onClick={() => handleDelete(fee.id!)}
                        disabled={deleteMut.isPending}
                      >
                        {deleteMut.isPending ? 'Deleting...' : 'Delete'}
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {editing ? 'Edit Fee Configuration' : 'New Fee Configuration'}
            </DialogTitle>
          </DialogHeader>
          <FeeForm
            initial={editing}
            onClose={() => setOpen(false)}
            onSaved={() => setOpen(false)}
            workspaceId={workspaceId}
          />
        </DialogContent>
      </Dialog>
    </Card>
  );
}