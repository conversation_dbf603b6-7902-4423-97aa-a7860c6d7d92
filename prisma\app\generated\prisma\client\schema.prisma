generator client {
  provider        = "prisma-client-js"
  output          = "app/generated/prisma/client"
  previewFeatures = ["driverAdapters", "fullTextSearchPostgres", "postgresqlExtensions"]
}

datasource db {
  provider   = "postgresql"
  url        = env("DATABASE_URL")
  directUrl  = env("DATABASE_URL_UNPOOLED")
  extensions = [vector(schema: "public")]
}

model User {
  id                  String  @id
  name                String?
  email               String? @unique
  phone               String?
  plan                String  @default("basic")
  credits             Int     @default(3)
  image               String?
  language            String? @default("english")
  timezone            String  @default("Asia/Taipei") // Store timezone in IANA format
  onboardingEmailSent Boolean @default(false)

  bankAccounts      BankAccount[]
  budgets           Budget[]
  transactions      Transaction[]
  incomes           Income[]
  expenses          Expense[]
  budgetRollovers   BudgetRollover[]
  budgetPreferences BudgetPreferences[]

  createdAt DateTime @default(now()) @map(name: "created_at")
  updatedAt DateTime @default(now()) @map(name: "updated_at")

  stripeCustomerId       String?         @unique @map(name: "stripe_customer_id")
  stripeSubscriptionId   String?         @unique @map(name: "stripe_subscription_id")
  stripePriceId          String?         @map(name: "stripe_price_id")
  stripeCurrentPeriodEnd DateTime?       @map(name: "stripe_current_period_end")
  //workspaceId            String?
  members                Member[]
  projects               Project[]
  tasks                  Task[]
  Category               Category[]
  assets                 Asset[]
  investments            Investment[]
  liabilities            Liability[]
  savingsGoals           SavingsGoal[]
  chats                  Chat[]
  speeches               Speech[]
  banners                Banner[]
  meetingReports         MeetingReport[]

  @@map(name: "users")
}

model Workspace {
  id                   String                @id @default(cuid())
  name                 String
  orgnr                Int?
  address              String?
  postalCode           String?
  city                 String?
  inviteCode           String?
  //bank_accounts BankAccount[]
  members              Member[]
  projects             Project[]
  tasks                Task[]
  speeches             Speech[]
  meetingReports       MeetingReport[]
  residentPatterns     ResidentPattern[]
  inventories          Inventory[]
  residents            Resident[]
  sales                Sale[]
  stockMovements       StockMovement[]
  paymentRequests      PaymentRequest[]
  paymentAccounts      PaymentAccount[]
  // NEW relations for resident management
  parkingSpaces        ParkingSpace[]
  vehicleRegistrations VehicleRegistration[]
  feeConfigurations    FeeConfiguration[]
  managementFees       ManagementFee[]
  feePayments          FeePayment[]
  residentImports      ResidentImport[]
  ParkingUsages        ParkingUsage[]
  parkingUsagePayments ParkingUsagePayment[]

  createdAt DateTime @default(now()) @map(name: "created_at")
  updatedAt DateTime @default(now()) @map(name: "updated_at")
}

model Member {
  id            String  @id @default(cuid())
  userId        String
  bankAccountId String?
  workspaceId   String
  role          Role    @default(MEMBER) // Workspace-specific role

  user             User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  assignees        Task[]
  bankAccount      BankAccount?      @relation(fields: [bankAccountId], references: [id], onDelete: Cascade)
  workspace        Workspace         @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  residentPatterns ResidentPattern[]
  residentImports  ResidentImport[]
  stockMovements   StockMovement[]
  PaymentRequest   PaymentRequest[]
  PaymentAccount   PaymentAccount[]

  @@unique([userId, workspaceId]) // Ensure a user can't have multiple roles for the same workspace
}

model Project {
  id          String    @id @default(cuid())
  name        String
  userId      String
  workspaceId String
  imageUrl    String?
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  createdAt   DateTime  @default(now()) @map(name: "created_at")
  updatedAt   DateTime  @default(now()) @map(name: "updated_at")
  tasks       Task[]
  speeches    Speech[]
}

model Task {
  id          String     @id @default(cuid())
  name        String
  userId      String
  workspaceId String
  projectId   String
  assigneeId  String
  description String?
  user        User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  workspace   Workspace  @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  assignee    Member     @relation(fields: [assigneeId], references: [id])
  startDate   DateTime?
  dueDate     DateTime
  status      TaskStatus
  position    Int        @default(1000)

  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now()) @map(name: "created_at")
  updatedAt DateTime @default(now()) @map(name: "updated_at")

  reminders Reminder[] // One-to-many relationship with reminders
}

model Reminder {
  id            String        @id @default(cuid())
  taskId        String
  task          Task          @relation(fields: [taskId], references: [id], onDelete: Cascade)
  enabled       Boolean       @default(false) // Whether the reminder is active
  basis         ReminderBasis // START_DATE or DUE_DATE
  daysBefore    Int // Days before the basis date to send a reminder
  customDate    DateTime? // Custom reminder date
  workflowRunId String? // Track workflow runs for dynamic updates
  workflowRuns  WorkflowRun[] // One-to-many relationship with workflow runs
  createdAt     DateTime      @default(now()) @map(name: "created_at")
  updatedAt     DateTime      @default(now()) @map(name: "updated_at")
}

enum ReminderBasis {
  START_DATE
  DUE_DATE
}

model WorkflowRun {
  id            String   @id @default(cuid())
  reminderId    String
  reminder      Reminder @relation(fields: [reminderId], references: [id], onDelete: Cascade)
  workflowRunId String   @unique // Workflow ID from Upstash
  status        String // Workflow status (e.g., "active", "completed")
  createdAt     DateTime @default(now()) @map(name: "created_at")
  updatedAt     DateTime @default(now()) @map(name: "updated_at")
}

model BankAccount {
  id              String           @id @default(cuid())
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @default(now())
  resourceId      String?
  originalId      String?
  orgId           String?
  userId          String?
  name            String
  originalPayload Json?
  initialAmount   Int?             @default(0)
  Balance         Balance[]
  TimeDeposit     TimeDeposit[]
  Transaction     Transaction[]
  Income          Income[]
  Expense         Expense[]
  Budget          Budget[]
  BudgetRollover  BudgetRollover[]
  accountType     AccountType      @default(BANK)
  resource        Resource?        @relation(fields: [resourceId], references: [id], onDelete: Cascade)
  //workspaceId     String?          
  //workspace       Workspace?       @relation(fields: [workspaceId], references: [id])
  members         Member[]
  user            User?            @relation(fields: [userId], references: [id])

  @@map(name: "bank_accounts")
}

model Balance {
  id              String       @id @default(cuid())
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @default(now())
  accountId       String?
  assetId         String?
  currencyIso     String
  amount          Int?
  date            DateTime
  description     String?
  type            BalanceType  @default(AVAILABLE)
  originalPayload Json?
  bankAccount     BankAccount? @relation(fields: [accountId], references: [id], onDelete: Cascade)
  asset           Asset?       @relation(fields: [assetId], references: [id], onDelete: Cascade)
  currency        Currency     @relation(fields: [currencyIso], references: [iso], onDelete: Cascade)
}

model TimeDeposit {
  id                  String          @id @default(cuid())
  createdAt           DateTime        @default(now())
  updatedAt           DateTime        @default(now())
  accountId           String?
  assetId             String?
  categoryId          String?
  currencyIso         String
  amount              Int?
  date                DateTime
  certificateNo       String?
  period              String?
  interestRate        Decimal?
  description         String?
  type                TimeDepositType @default(AVAILABLE)
  originalPayload     Json?
  bankAccount         BankAccount?    @relation(fields: [accountId], references: [id], onDelete: Cascade)
  asset               Asset?          @relation(fields: [assetId], references: [id], onDelete: Cascade)
  currency            Currency        @relation(fields: [currencyIso], references: [iso], onDelete: Cascade)
  category            Category?       @relation(fields: [categoryId], references: [id])
  categoryValidated   Boolean         @default(false)
  suggestedCategoryId String?
}

model Currency {
  iso         String        @id
  symbol      String        @default("$")
  name        String        @default("新台幣")
  numericCode Int?
  Balance     Balance[]
  Transaction Transaction[]
  TimeDeposit TimeDeposit[]
}

model Asset {
  id              String             @id @default(cuid())
  name            String
  originalPayload Json?
  Balance         Balance[]
  TimeDeposit     TimeDeposit[]
  Transaction     Transaction[]
  type            AssetType
  value           Decimal
  purchaseDate    DateTime?
  description     String?
  userId          String
  user            User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions    AssetTransaction[]
  valuations      AssetValuation[]

  @@index([id, userId])
  @@map(name: "assets")
}

model AssetValuation {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  value     Decimal
  date      DateTime
  assetId   String
  asset     Asset    @relation(fields: [assetId], references: [id], onDelete: Cascade)

  @@map(name: "asset_valuations")
}

model AssetTransaction {
  id          String          @id @default(cuid())
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  type        TransactionType
  amount      Decimal
  date        DateTime
  description String?
  assetId     String
  asset       Asset           @relation(fields: [assetId], references: [id], onDelete: Cascade)

  @@map(name: "asset_transactions")
}

model Transaction {
  id                  String       @id @default(cuid())
  createdAt           DateTime     @default(now())
  updatedAt           DateTime     @default(now())
  accountId           String?
  assetId             String?
  userId              String
  user                User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  currencyIso         String
  categoryId          String?
  amount              Decimal
  date                DateTime
  description         String
  originalPayload     Json?
  review              Boolean      @default(false)
  bankAccount         BankAccount? @relation(fields: [accountId], references: [id], onDelete: Cascade)
  asset               Asset?       @relation(fields: [assetId], references: [id], onDelete: Cascade)
  category            Category?    @relation(fields: [categoryId], references: [id])
  currency            Currency     @relation(fields: [currencyIso], references: [iso], onDelete: Cascade)
  type                String
  categoryValidated   Boolean      @default(false)
  suggestedCategoryId String?

  @@index([id, userId, accountId, categoryId, type])
}

model Income {
  id          String   @id @default(cuid())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  amount      Decimal
  description String?
  date        DateTime
  categoryId  String
  category    Category @relation(fields: [categoryId], references: [id])

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  accountId           String?
  bankAccount         BankAccount? @relation(fields: [accountId], references: [id])
  type                String
  categoryValidated   Boolean      @default(false)
  suggestedCategoryId String?
  importHash          String?      @unique

  @@unique([userId, accountId, categoryId, date, amount, description])
  @@index([id, userId, accountId, categoryId, importHash])
}

model Expense {
  id          String   @id @default(cuid())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  amount      Decimal
  description String?
  date        DateTime
  categoryId  String
  category    Category @relation(fields: [categoryId], references: [id])

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  accountId           String?
  bankAccount         BankAccount? @relation(fields: [accountId], references: [id])
  type                String
  categoryValidated   Boolean      @default(false)
  suggestedCategoryId String?
  importHash          String?      @unique

  @@unique([userId, accountId, categoryId, date, amount, description])
  @@index([id, userId, accountId, categoryId, importHash])
}

model SpreadsheetImport {
  id         String   @id @default(cuid())
  userId     String
  importedAt DateTime @default(now())
  fileName   String?
  sheetName  String
  rowCount   Int
  type       String // "Income", "Expense", or "Both"
  importHash String

  @@unique([userId, sheetName, importHash])
}

enum CategoryType {
  CREDIT
  DEBIT
}

model Category {
  id              String           @id @default(cuid())
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  name            String
  icon            String
  color           String           @default("hsl(var(--chart-1))")
  keywords        String[]         @default([])
  type            CategoryType     @default(DEBIT)
  userId          String
  description     String?
  user            User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions    Transaction[]
  budgets         CategoryBudget[]
  incomes         Income[]
  expenses        Expense[]
  budgetRollovers BudgetRollover[]
  TimeDeposit     TimeDeposit[]

  @@unique([name, userId])
  @@index([id, userId])
  @@map(name: "categories")
}

model BudgetPreferences {
  id                   String  @id @default(cuid())
  userId               String  @unique
  user                 User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  autoCreateNextBudget Boolean @default(true)
  autoApplyRollovers   Boolean @default(true)
  rolloverThreshold    Decimal @default(0)
}

model Budget {
  id                String           @id @default(cuid())
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  name              String?
  startDate         DateTime?
  endDate           DateTime?
  amount            Decimal?
  userId            String
  user              User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  rolloversPrevious BudgetRollover[] @relation("PreviousBudget")
  rolloversNext     BudgetRollover[] @relation("NextBudget")
  categories        CategoryBudget[]
  accountId         String?
  account           BankAccount?     @relation(fields: [accountId], references: [id])

  @@index([id, userId, accountId])
  @@map(name: "budgets")
}

model CategoryBudget {
  id         String   @id @default(cuid())
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  amount     Decimal
  budgetId   String
  categoryId String
  budget     Budget   @relation(fields: [budgetId], references: [id], onDelete: Cascade)
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@unique([budgetId, categoryId])
  @@index([id, budgetId])
  @@map(name: "category_budgets")
}

model BudgetRollover {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  categoryId String
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  previousBudgetId String?
  previousBudget   Budget? @relation("PreviousBudget", fields: [previousBudgetId], references: [id], onDelete: SetNull)

  nextBudgetId String?
  nextBudget   Budget? @relation("NextBudget", fields: [nextBudgetId], references: [id], onDelete: SetNull)

  amount             Decimal // Rolled over amount
  createdAt          DateTime     @default(now())
  periodStart        DateTime
  periodEnd          DateTime
  rolloverPercentage Decimal?
  accountId          String?
  account            BankAccount? @relation(fields: [accountId], references: [id])

  @@index([id, userId, accountId, categoryId])
}

model ConnectorConfig {
  id          String       @id @default(cuid())
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  orgId       String
  secret      Json?
  env         ConnectorEnv
  connectorId String
  connector   Connector    @relation(fields: [connectorId], references: [id], onDelete: Cascade)
}

model Connector {
  id              String            @id @default(cuid())
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  name            String
  logoUrl         String?
  status          ConnectorStatus
  type            ConnectorType
  connectorConfig ConnectorConfig[]
  Integration     Integration[]
}

model Integration {
  id                  String     @id @default(cuid())
  createdAt           DateTime   @default(now())
  updatedAt           DateTime   @updatedAt
  name                String
  logoUrl             String?
  connectorProviderId String?    @unique
  connectorId         String
  connector           Connector  @relation(fields: [connectorId], references: [id], onDelete: Cascade)
  Resource            Resource[]
}

model Resource {
  id            String        @id @default(cuid())
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  integrationId String
  originalId    String
  userId        String
  integration   Integration   @relation(fields: [integrationId], references: [id], onDelete: Cascade)
  bankAccounts  BankAccount[]

  @@map(name: "resources")
}

model Stats {
  id                     String   @id @default(cuid())
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt
  totalUsers             Int      @default(0)
  activeUsers            Int      @default(0)
  totalAccounts          Int      @default(0)
  totalTransactions      Int      @default(0)
  totalAssets            Int      @default(0)
  totalLiabilities       Int      @default(0)
  totalInvestments       Int      @default(0)
  avgAccountsPerUser     Decimal  @default(0)
  avgTransactionsPerUser Decimal  @default(0)
  dailyActiveUsers       Int      @default(0)
  weeklyActiveUsers      Int      @default(0)
  monthlyActiveUsers     Int      @default(0)
  operatingSystem        Json? // Store counts for different OS
  browser                Json? // Store counts for different browsers
  country                Json? // Store counts for different countries
  lastUpdated            DateTime @default(now())

  @@map(name: "stats")
}

model StatsHistory {
  id                 String   @id @default(cuid())
  createdAt          DateTime @default(now())
  totalUsers         Int
  activeUsers        Int
  totalAccounts      Int
  totalTransactions  Int
  totalAssets        Int
  totalLiabilities   Int
  totalInvestments   Int
  dailyActiveUsers   Int
  weeklyActiveUsers  Int
  monthlyActiveUsers Int
  snapshot           Json // Store full stats snapshot

  @@map(name: "stats_history")
}

model Investment {
  id            String                  @id @default(cuid())
  createdAt     DateTime                @default(now())
  updatedAt     DateTime                @updatedAt
  name          String
  type          InvestmentType
  amount        Decimal
  shares        Decimal?
  purchasePrice Decimal?
  currentPrice  Decimal?
  description   String?
  userId        String
  user          User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions  InvestmentTransaction[]
  valuations    InvestmentValuation[]

  @@index([id, userId])
  @@map(name: "investments")
}

model InvestmentValuation {
  id           String     @id @default(cuid())
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  value        Decimal
  date         DateTime
  investmentId String
  investment   Investment @relation(fields: [investmentId], references: [id], onDelete: Cascade)

  @@index([id, investmentId])
  @@map(name: "investment_valuations")
}

model InvestmentTransaction {
  id           String                    @id @default(cuid())
  createdAt    DateTime                  @default(now())
  updatedAt    DateTime                  @updatedAt
  type         InvestmentTransactionType
  amount       Decimal
  shares       Decimal?
  price        Decimal
  date         DateTime
  description  String?
  investmentId String
  investment   Investment                @relation(fields: [investmentId], references: [id], onDelete: Cascade)

  @@index([id, investmentId])
  @@map(name: "investment_transactions")
}

model Liability {
  id             String             @id @default(cuid())
  createdAt      DateTime           @default(now())
  updatedAt      DateTime           @updatedAt
  name           String
  type           LiabilityType
  amount         Decimal
  interestRate   Decimal
  monthlyPayment Decimal
  startDate      DateTime?
  endDate        DateTime?
  description    String?
  userId         String
  user           User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  payments       LiabilityPayment[]

  @@index([id, userId])
  @@map(name: "liabilities")
}

model LiabilityPayment {
  id          String      @id @default(cuid())
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  amount      Decimal
  date        DateTime
  type        PaymentType
  description String?
  liabilityId String
  liability   Liability   @relation(fields: [liabilityId], references: [id], onDelete: Cascade)

  @@map(name: "liability_payments")
}

model SavingsGoal {
  id          String            @id @default(cuid())
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  name        String
  target      Decimal
  current     Decimal           @default(0)
  deadline    DateTime?
  description String?
  isDefault   Boolean           @default(false) // To distinguish between system defaults and user goals
  type        GoalType          @default(CUSTOM)
  priority    Int               @default(0) // For ordering goals
  userId      String
  user        User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  progress    SavingsProgress[]

  @@index([id, userId])
  @@map(name: "savings_goals")
}

model SavingsProgress {
  id        String      @id @default(cuid())
  createdAt DateTime    @default(now())
  amount    Decimal
  date      DateTime
  goalId    String
  goal      SavingsGoal @relation(fields: [goalId], references: [id], onDelete: Cascade)

  @@index([id, goalId])
  @@map(name: "savings_progress")
}

model ContractAnalysis {
  id                          String                 @id @default(cuid())
  userId                      String
  contractText                String
  summary                     String
  recommendations             String[]
  keyClauses                  String[]
  legalCompliance             String
  negotiationPoints           String[]
  contractDuration            String
  terminationConditions       String
  overallScore                Float
  performanceMetrics          String[]
  intellectualPropertyClauses Json?
  createdAt                   DateTime               @default(now())
  version                     Int                    @default(1)
  userFeedback                Json?
  customFields                Json?
  expirationDate              DateTime?
  language                    String                 @default("en")
  filePath                    String?
  contractType                String
  financialTerms              Json?
  specificClauses             String
  compensationStructure       CompensationStructure?
  opportunities               Opportunity[]
  risks                       Risk[]
  chats                       Chat[]

  @@index([userId])
}

model Risk {
  id          String           @id @default(cuid())
  contractId  String
  risk        String
  explanation String
  severity    String
  contract    ContractAnalysis @relation(fields: [contractId], references: [id], onDelete: Cascade)
}

model Opportunity {
  id          String           @id @default(cuid())
  contractId  String
  opportunity String
  explanation String
  impact      String
  contract    ContractAnalysis @relation(fields: [contractId], references: [id], onDelete: Cascade)
}

model CompensationStructure {
  id            String           @id @default(cuid())
  contractId    String           @unique
  baseSalary    String
  bonuses       String
  equity        String
  otherBenefits String
  contract      ContractAnalysis @relation(fields: [contractId], references: [id], onDelete: Cascade)
}

model Speech {
  id             String          @id @default(cuid())
  userId         String
  workspaceId    String
  projectId      String
  title          String
  description    String?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  user           User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  workspace      Workspace       @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  project        Project         @relation(fields: [projectId], references: [id], onDelete: Cascade)
  recordings     Recording[]
  transcriptions Transcription[]
  analyses       Analysis[]
}

model Recording {
  id        String   @id @default(cuid())
  speechId  String
  speech    Speech   @relation(fields: [speechId], references: [id], onDelete: Cascade)
  url       String // URL to the stored audio file
  duration  Int // Duration in seconds
  createdAt DateTime @default(now())
}

model Transcription {
  id        String   @id @default(cuid())
  speechId  String
  speech    Speech   @relation(fields: [speechId], references: [id], onDelete: Cascade)
  text      String // Full transcribed text
  language  String // Language code (e.g., "en", "zh-TW")
  createdAt DateTime @default(now())
}

model Analysis {
  id        String   @id @default(cuid())
  speechId  String
  speech    Speech   @relation(fields: [speechId], references: [id], onDelete: Cascade)
  summary   String? // AI-generated summary
  keyPoints Json? // Key points or action items extracted by AI
  sentiment String? // Sentiment analysis (e.g., "Positive", "Neutral", "Negative")
  createdAt DateTime @default(now())
}

model VectorStore {
  id           String                      @id @default(uuid())
  documentId   String
  documentType String
  content      String
  embedding    Unsupported("vector(384)")?
  metadata     Json?
  filePath     String
  createdAt    DateTime                    @default(now())

  @@index([documentId])
  @@index([filePath])
  @@index([documentType])
}

model Assistant {
  assistant_id String   @id @default(cuid())
  graph_id     String
  config       Json
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt
  metadata     Json?
  version      Int?
  name         String
  description  String?
  runs         Run[]    @relation("AssistantRuns")
  chats        Chat[]   @relation("AssistantChats")

  @@unique([graph_id, name]) // Ensure uniqueness of graph_id + name
}

model Thread {
  thread_id         String             @id @default(cuid())
  created_at        DateTime           @default(now())
  updated_at        DateTime           @updatedAt
  metadata          Json?
  status            ThreadStatus
  values            Json
  checkpoints       Checkpoint[]
  checkpoint_blobs  CheckpointBlobs[]
  checkpoint_writes CheckpointWrites[]
  runs              Run[]              @relation("ThreadRuns")
}

model Run {
  run_id             String             @id @default(cuid())
  thread             Thread             @relation(fields: [thread_id], references: [thread_id], onDelete: Cascade, name: "ThreadRuns")
  thread_id          String
  assistant          Assistant          @relation(fields: [assistant_id], references: [assistant_id], onDelete: Cascade, name: "AssistantRuns")
  assistant_id       String
  created_at         DateTime           @default(now())
  updated_at         DateTime           @updatedAt
  status             RunStatus
  metadata           Json?
  multitask_strategy MultitaskStrategy?
}

model Checkpoint {
  thread               Thread  @relation(fields: [thread_id], references: [thread_id], onDelete: Cascade)
  thread_id            String
  checkpoint_ns        String
  checkpoint_id        String
  parent_checkpoint_id String?
  type                 String?
  checkpoint           Json
  metadata             Json?   @default("{}")

  @@id([thread_id, checkpoint_ns, checkpoint_id])
}

model CheckpointBlobs {
  thread        Thread @relation(fields: [thread_id], references: [thread_id], onDelete: Cascade)
  thread_id     String
  checkpoint_ns String @default("")
  channel       String
  version       String
  type          String
  blob          Bytes?

  @@id([thread_id, checkpoint_ns, channel, version])
  @@map("checkpoint_blobs")
}

model CheckpointWrites {
  thread        Thread  @relation(fields: [thread_id], references: [thread_id], onDelete: Cascade)
  thread_id     String
  checkpoint_ns String  @default("")
  checkpoint_id String
  task_id       String
  idx           Int
  channel       String
  type          String?
  blob          Bytes

  @@id([thread_id, checkpoint_ns, checkpoint_id, task_id, idx])
  @@map("checkpoint_writes")
}

model CheckpointMigrations {
  v Int @id

  @@map("checkpoint_migrations")
}

model Chat {
  id          String     @id @default(cuid())
  assistantId String
  contractId  String?
  assistant   Assistant  @relation(fields: [assistantId], references: [assistant_id], onDelete: Cascade, name: "AssistantChats")
  createdAt   DateTime   @default(now())
  title       String
  userId      String
  visibility  Visibility @default(PRIVATE) // Equivalent to varchar('visibility', { enum: ['public', 'private'] }).notNull().default('private')

  user      User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  contracts ContractAnalysis? @relation(fields: [contractId], references: [id], onDelete: Cascade)
  messages  Message[]

  @@map("Chat")
}

model Message {
  id        String   @id @default(cuid())
  chatId    String
  role      String
  content   Json
  createdAt DateTime @default(now())

  chat Chat @relation(fields: [chatId], references: [id], onDelete: Cascade)

  @@map("Message")
}

model MemoryStore {
  id         String   @id @default(cuid())
  namespace  String[]
  key        String
  value      Json
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@unique([namespace, key])
}

model Banner {
  id          String   @id @default(cuid())
  userId      String?
  user        User?    @relation(fields: [userId], references: [id], onDelete: Cascade)
  title       String
  type        String?  @default("title")
  size        String?  @default("text-base")
  fontWeight  String?  @default("font-normal")
  color       String
  description String?
  shared      Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([userId])
}

// Enum for item types to ensure data consistency
enum ItemType {
  A_BUILDING_CARD          @map("A棟磁扣")
  B_BUILDING_CARD          @map("B棟磁扣")
  ALL_AREA_CARD            @map("全區磁扣")
  MAIN_GATE_REMOTE_CONTROL @map("大門遙控器")

  @@map("item_types")
}

// Inventory table to track current stock levels
model Inventory {
  id       String   @id @default(cuid())
  itemType ItemType @map("item_type")
  current  Int      @default(0) // Current stock count
  price    Int // Price in NT$ (stored as integer, divide by 100 for decimal)
  minStock Int      @default(0) @map("min_stock") // Minimum stock alert level
  maxStock Int      @default(100) @map("max_stock") // Maximum stock capacity

  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  saleItems      SaleItem[]
  stockMovements StockMovement[]

  @@unique([itemType, workspaceId])
  @@map("inventory")
}

// Residents table for customer information
model Resident {
  id String @id @default(cuid())

  // Basic Information
  name    String
  address String // Keep for backward compatibility
  phone   String?
  email   String?

  // Building and unit information
  building String? // A棟, B棟, etc.
  floor    String?
  unit     String?

  // NEW FIELDS (with defaults to preserve existing data)
  residentId    String   @default("") @map("resident_id") // 住戶編號 (e.g., "06-01-1")
  squareFootage Decimal? @map("square_footage") // 坪數
  paymentMethod String?  @default("匯款") @map("payment_method")
  isCombined    Boolean  @default(false) @map("is_combined") // 合併 (Y/N)

  // Type of resident
  type ResidentType @default(OWNER)

  // For tenants - link to owner's resident record
  ownerResidentId String?    @map("owner_resident_id")
  ownerResident   Resident?  @relation("OwnerTenant", fields: [ownerResidentId], references: [id], onDelete: SetNull)
  tenants         Resident[] @relation("OwnerTenant")

  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  salesAsPurchaser         Sale[]                @relation("SalePurchaser") // Sales where this person was the purchaser
  salesForUnit             Sale[] // Sales for this resident unit
  parkingSpaces            ParkingSpace[]
  vehicleRegistrations     VehicleRegistration[]
  managementFees           ManagementFee[]
  feePayments              FeePayment[]
  ParkingUsages            ParkingUsage[]        @relation("ParkingRenter") // Parking spaces rented by this resident
  customParkingFeePayments ParkingUsage[]        @relation("CustomParkingFeePayer") // Custom fee payer

  @@unique([residentId, workspaceId])
  @@index([workspaceId, type])
  @@index([workspaceId, residentId])
  @@map("residents")
}

// Main sales transaction table
model Sale {
  id          String   @id @default(cuid())
  date        DateTime @default(now())
  receiptNo   String?  @unique @map("receipt_no") // Receipt number if provided
  recipient   String? // Person who received the payment
  notes       String?
  totalAmount Int      @map("total_amount") // Total sale amount

  // Property Unit Reference (住戶編號) - Always links to the unit
  residentId String   @map("resident_id")
  resident   Resident @relation(fields: [residentId], references: [id])

  // Purchaser Information (NEW - who actually bought/paid)
  purchaserId   String?       @map("purchaser_id") // Links to owner or tenant record
  purchaser     Resident?     @relation("SalePurchaser", fields: [purchaserId], references: [id], onDelete: SetNull)
  purchaserType ResidentType? @map("purchaser_type") // OWNER or TENANT
  purchaserName String        @default("") @map("purchaser_name") // Stored for historical record

  // Keep for backward compatibility / historical records
  address String // Store address directly for historical records

  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @map("updated_at")

  // Relations
  saleItems SaleItem[]

  @@index([workspaceId, date])
  @@index([workspaceId, residentId])
  @@index([workspaceId, purchaserId])
  @@map("sales")
}

// Individual sale items (line items in a sale)
model SaleItem {
  id String @id @default(cuid())

  // Sale reference
  saleId String @map("sale_id")
  sale   Sale   @relation(fields: [saleId], references: [id], onDelete: Cascade)

  // Inventory reference
  inventoryId String    @map("inventory_id")
  inventory   Inventory @relation(fields: [inventoryId], references: [id])

  // Item details at time of sale (for historical accuracy)
  itemType  ItemType
  quantity  Int
  unitPrice Int      @map("unit_price") // Price per unit
  lineTotal Int      @map("line_total") // quantity * unitPrice

  createdAt DateTime @default(now()) @map("created_at")

  @@map("sale_items")
}

// Track all stock movements (purchases, sales, adjustments)
model StockMovement {
  id String @id @default(cuid())

  inventoryId String    @map("inventory_id")
  inventory   Inventory @relation(fields: [inventoryId], references: [id])

  type      StockMovementType
  quantity  Int // Positive for additions, negative for reductions
  reason    String? // Reason for the movement
  reference String? // Reference to sale ID, purchase order, etc.

  // Stock levels before and after the movement
  previousStock Int @map("previous_stock")
  newStock      Int @map("new_stock")

  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  memberId    String
  member      Member    @relation(fields: [memberId], references: [id])

  createdAt DateTime @default(now()) @map("created_at")

  @@map("stock_movements")
}

enum StockMovementType {
  SALE // Stock reduction due to sale
  PURCHASE // Stock increase due to purchase/restock
  ADJUSTMENT // Manual stock adjustment
  RETURN // Stock return
  DAMAGE // Stock damaged/lost
  INITIAL // Initial stock setup

  @@map("stock_movement_types")
}

model PaymentRequest {
  id        String   @id @default(cuid())
  name      String // 社區名稱
  year      String // 年度
  month     String // 月份
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  payments        Payment[]
  // Many-to-many relationship with PaymentAccount
  paymentAccounts PaymentRequestAccount[]

  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  memberId    String
  member      Member    @relation(fields: [memberId], references: [id], onDelete: NoAction)

  @@unique([name, year, month, workspaceId])
  @@map("payment_requests")
}

// Junction table for many-to-many relationship
model PaymentRequestAccount {
  id               String         @id @default(cuid())
  paymentRequest   PaymentRequest @relation(fields: [paymentRequestId], references: [id], onDelete: Cascade)
  paymentRequestId String
  paymentAccount   PaymentAccount @relation(fields: [paymentAccountId], references: [id], onDelete: Cascade)
  paymentAccountId String

  // Optional: Add context-specific fields if needed
  isPreferred Boolean @default(false) // Whether this account is preferred for this specific payment request

  createdAt DateTime @default(now())

  @@unique([paymentRequestId, paymentAccountId])
  @@map("payment_request_accounts")
}

model PaymentAccount {
  id            String             @id @default(cuid())
  accountType   PaymentAccountType // REMITTER, PAYEE, or BOTH
  bankCode      String // 銀行代號
  bankName      String // 銀行名稱
  branchName    String // 分行名稱
  accountNumber String // 帳號
  accountName   String // 戶名
  isDefault     Boolean            @default(false) // 是否為預設帳戶
  isActive      Boolean            @default(true) // 是否啟用

  // Many-to-many relationship with PaymentRequest
  paymentRequests PaymentRequestAccount[]

  // For payments using this account
  paymentsAsRemitter Payment[] @relation("PaymentRemitterAccount")
  paymentsAsPayee    Payment[] @relation("PaymentPayeeAccount")

  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  memberId    String
  member      Member    @relation(fields: [memberId], references: [id], onDelete: NoAction)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Optional: Add uniqueness constraint for account details if needed
  @@unique([bankCode, accountNumber, workspaceId])
  @@map("payment_accounts")
}

model Payment {
  id                String  @id @default(cuid())
  sequenceId        String // 序號 like "1140801"
  accountingSubject String // 會計科目
  payee             String // 支出摘要/受款人
  remarks           String? // 銀行備註
  month             String // 月份
  amount            Int // 金額
  paymentMethod     String  @default("匯款□/領現") // 付款方式

  showAccountInfo Boolean @default(false)

  // Relations to bank accounts (unchanged)
  remitterAccount   PaymentAccount? @relation("PaymentRemitterAccount", fields: [remitterAccountId], references: [id])
  remitterAccountId String?

  payeeAccount   PaymentAccount? @relation("PaymentPayeeAccount", fields: [payeeAccountId], references: [id])
  payeeAccountId String?

  // Relations
  paymentRequest   PaymentRequest @relation(fields: [paymentRequestId], references: [id], onDelete: Cascade)
  paymentRequestId String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("payments")
}

model MeetingReport {
  id         String     @id @default(cuid())
  title      String
  slug       String     @unique
  content    String
  reportType ReportType @default(MONTHLY)
  reportDate DateTime
  published  Boolean    @default(true)

  // 新增 workspace 關聯
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  // 使用新的可見性枚舉
  visibility ContentVisibility @default(WORKSPACE_MEMBERS) // 預設僅工作區成員可見

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("meeting_reports")
}

model ResidentPattern {
  id          String    @id @default(cuid())
  residentId  String // e.g., '06-01-1' or 'split:06-11-1,08-11-1'
  patterns    String[] // Array of regex patterns as strings
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  memberId    String
  member      Member    @relation(fields: [memberId], references: [id], onDelete: Cascade)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@unique([workspaceId, residentId])
  @@map(name: "resident_patterns")
}

// Parking Space Management
model ParkingSpace {
  id          String           @id @default(cuid())
  spaceNumber String           @map("space_number") // 車位號碼 (e.g., "21", "22")
  spaceType   ParkingSpaceType @default(CAR) @map("space_type")

  // Link to resident (OWNER of the parking space)
  residentId String   @map("resident_id")
  resident   Resident @relation(fields: [residentId], references: [id], onDelete: Cascade)

  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  isActive Boolean @default(true) @map("is_active")
  notes    String?

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  vehicleRegistrations VehicleRegistration[]
  ParkingUsages        ParkingUsage[] // NEW: Track usages

  @@unique([spaceType, spaceNumber, workspaceId])
  @@index([workspaceId, residentId])
  @@index([workspaceId, spaceType])
  @@map("parking_spaces")
}

// Vehicle Registration (for all vehicles including cars)
model VehicleRegistration {
  id String @id @default(cuid())

  // Vehicle Information
  licensePlate String      @map("license_plate") // 車牌號碼
  vehicleType  VehicleType @map("vehicle_type")
  brand        String? // 車輛廠牌
  model        String? // 車輛型號
  color        String? // 車輛顏色

  // Link to parking space and resident (OWNER of the parking space)
  parkingSpaceId String       @map("parking_space_id")
  parkingSpace   ParkingSpace @relation(fields: [parkingSpaceId], references: [id], onDelete: Cascade)

  residentId String   @map("resident_id") // Owner of the parking space
  resident   Resident @relation(fields: [residentId], references: [id], onDelete: Cascade)

  // Payment tracking (for motorcycle/bicycle annual fees)
  // NOTE: If parking space is rented, payment responsibility transfers to renter
  annualFee  Int?      @map("annual_fee") // 金額
  paidDate   DateTime? @map("paid_date") // 繳費日
  expiryDate DateTime? @map("expiry_date") // 截止日

  // NEW: Designate who pays the fee for this vehicle
  parkingFeePayer ParkingFeePayer @default(OWNER) @map("parking_fee_payer")

  // Registration dates
  registrationDate DateTime @default(now()) @map("registration_date") // 登記日期

  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  isActive Boolean @default(true) @map("is_active")
  notes    String?

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // NEW: Usages relationship
  ParkingUsage ParkingUsage?

  @@unique([licensePlate, workspaceId])
  @@index([workspaceId, residentId])
  @@index([workspaceId, parkingSpaceId])
  @@index([workspaceId, vehicleType])
  @@map("vehicle_registrations")
}

// NEW: Parking Space Usage Tracking
model ParkingUsage {
  id String @id @default(cuid())

  // Parking space being rented
  parkingSpaceId String       @unique @map("parking_space_id")
  parkingSpace   ParkingSpace @relation(fields: [parkingSpaceId], references: [id], onDelete: Cascade)

  // Optional: Link to vehicle registration if renter registers their vehicle
  vehicleRegistrationId String?              @unique @map("vehicle_registration_id")
  vehicleRegistration   VehicleRegistration? @relation(fields: [vehicleRegistrationId], references: [id], onDelete: SetNull)

  // Renter information
  renterName     String  @map("renter_name")
  renterPhone    String? @map("renter_phone")
  renterEmail    String? @map("renter_email")
  renterIdNumber String? @map("renter_id_number") // 身份證號 (optional)
  renterAddress  String? @map("renter_address")

  // Optional: Link to resident if renter is another resident in the building
  renterResidentId String?   @map("renter_resident_id")
  renterResident   Resident? @relation("ParkingRenter", fields: [renterResidentId], references: [id], onDelete: SetNull)

  // Usage terms
  monthlyRent     Int       @map("monthly_rent") // Usage amount
  securityDeposit Int?      @map("security_deposit") // Deposit
  startDate       DateTime  @map("start_date")
  endDate         DateTime? @map("end_date") // NULL for ongoing usages

  // REDESIGNED: Parking Fee Payment Responsibility
  // This determines WHO pays the monthly parking management fee (not the usage fee)
  // Example scenarios:
  // 1. OWNER pays: Owner rents out space but still pays parking fee to management
  // 2. RENTER pays: Renter pays both usage fee AND parking management fee
  parkingFeePayer ParkingFeePayer @default(RENTER) @map("parking_fee_payer")

  // Optional: Specific payer if different from owner/renter
  // Use this when fee should be paid by someone else (rare cases)
  customPayerResidentId String?   @map("custom_payer_resident_id")
  customPayer           Resident? @relation("CustomParkingFeePayer", fields: [customPayerResidentId], references: [id], onDelete: SetNull)
  customPayerName       String?   @map("custom_payer_name") // If not a resident

  // Contract information
  contractNumber     String?   @map("contract_number")
  contractSignedDate DateTime? @map("contract_signed_date")

  // Status
  status UsageStatus @default(ACTIVE)

  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  notes String?

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  usagePayments ParkingUsagePayment[] // Usage income payments (to owner)

  @@index([workspaceId, status])
  @@index([workspaceId, renterResidentId])
  @@index([workspaceId, parkingFeePayer])
  @@index([workspaceId, startDate, endDate])
  @@map("parking_usages")
}

// Parking Usage Payment Records (Usage Income to management)
model ParkingUsagePayment {
  id String @id @default(cuid())

  usageId String       @map("usage_id")
  usage   ParkingUsage @relation(fields: [usageId], references: [id], onDelete: Cascade)

  amount        Int // Usage payment amount (paid TO management)
  paymentDate   DateTime @map("payment_date")
  paymentMethod String?  @map("payment_method")
  receiptNo     String?  @map("receipt_no")

  // Period this usage payment covers
  periodStart DateTime @map("period_start")
  periodEnd   DateTime @map("period_end")

  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  notes String?

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@index([workspaceId, usageId])
  @@index([workspaceId, paymentDate])
  @@map("parking_usage_payments")
}

// Management Fee Configuration
model FeeConfiguration {
  id String @id @default(cuid())

  feeType      FeeType      @map("fee_type")
  vehicleType  VehicleType? // NULL for non-vehicle fees
  unitPrice    Int          @map("unit_price") // Price
  billingCycle BillingCycle @default(MONTHLY) @map("billing_cycle") // Monthly or Annual
  description  String?

  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  isActive      Boolean   @default(true) @map("is_active")
  effectiveFrom DateTime  @default(now()) @map("effective_from")
  effectiveTo   DateTime? @map("effective_to")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@unique([feeType, workspaceId, effectiveFrom])
  @@map("fee_configurations")
}

// Management Fee Records
model ManagementFee {
  id String @id @default(cuid())

  residentId String   @map("resident_id")
  resident   Resident @relation(fields: [residentId], references: [id], onDelete: Cascade)

  // Fee breakdown
  feeType     FeeType @map("fee_type")
  baseFee     Int     @map("base_fee") // 管理費
  parkingFee  Int     @default(0) @map("parking_fee") // 汽車管理費
  vehicleFee  Int     @default(0) @map("vehicle_fee") // 機踏車管理費
  totalAmount Int     @map("total_amount") // 應繳金額

  // Calculation details
  squareFootage     Decimal? @map("square_footage") // 坪數 at time of calculation
  squareFootageRate Int?     @map("square_footage_rate") // Rate at time of calculation
  parkingSpaceCount Int      @default(0) @map("parking_space_count")
  vehicleCount      Int      @default(0) @map("vehicle_count")

  // Period
  billingPeriod String   @map("billing_period") // e.g., "2025-01", "2025-Q1"
  dueDate       DateTime @map("due_date")

  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  status FeeStatus @default(PENDING)
  notes  String?

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  payments FeePayment[]

  @@unique([residentId, billingPeriod, feeType])
  @@index([workspaceId, billingPeriod, status])
  @@map("management_fees")
}

// Fee Payment Records
model FeePayment {
  id String @id @default(cuid())

  managementFeeId String        @map("management_fee_id")
  managementFee   ManagementFee @relation(fields: [managementFeeId], references: [id], onDelete: Cascade)

  // Payer information (can be owner or tenant)
  payerId   String       @map("payer_id")
  payer     Resident     @relation(fields: [payerId], references: [id], onDelete: Cascade)
  payerType ResidentType @map("payer_type") // Track if owner or tenant paid

  amount        Int // Amount paid
  paymentDate   DateTime @map("payment_date")
  paymentMethod String?  @map("payment_method") // 7-ELEVEN, ATM, etc.
  receiptNo     String?  @map("receipt_no")

  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  notes String?

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@index([managementFeeId])
  @@index([payerId])
  @@map("fee_payments")
}

// Import History for tracking CSV/XLSX imports
model ResidentImport {
  id String @id @default(cuid())

  fileName      String         @map("file_name")
  fileType      ImportFileType @map("file_type")
  rowsProcessed Int            @map("rows_processed")
  rowsSuccess   Int            @map("rows_success")
  rowsFailed    Int            @map("rows_failed")

  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  memberId String @map("member_id")
  member   Member @relation(fields: [memberId], references: [id])

  errors Json? // Store any import errors

  createdAt DateTime @default(now()) @map("created_at")

  @@map("resident_imports")
}

// ==================== ENUMS ====================

enum ResidentType {
  OWNER // 自住
  TENANT // 租客

  @@map("resident_types")
}

enum ParkingSpaceType {
  CAR // 汽車位
  MOTORCYCLE // 機車位
  BICYCLE // 自行車位

  @@map("parking_space_types")
}

enum VehicleType {
  CAR // 汽車
  BICYCLE // 自行車 (500/year)
  MOTORCYCLE // 機車 (1000/year)
  HEAVY_MOTORCYCLE // 重型機車 (1500/year)

  @@map("vehicle_types")
}

enum FeeType {
  MANAGEMENT // 管理費 (by 坪數 * rate)
  PARKING // 汽車管理費 (deprecated - use PARKING_CAR)
  VEHICLE // 機踏車管理費 (deprecated - use specific types)
  COMBINED // 合併費用 (deprecated)
  PARKING_CAR // 汽車停車費 (by car parking space count)
  VEHICLE_BICYCLE // 自行車費 (annual)
  VEHICLE_MOTORCYCLE // 機車費 (annual)
  VEHICLE_HEAVY_MOTORCYCLE // 重型機車費 (annual)

  @@map("fee_types")
}

enum FeeStatus {
  PENDING // 待繳
  PARTIAL // 部分繳納
  PAID // 已繳
  OVERDUE // 逾期
  WAIVED // 免除

  @@map("fee_statuses")
}

enum BillingCycle {
  MONTHLY // 月繳
  ANNUAL // 年繳
  QUARTERLY // 季繳

  @@map("billing_cycles")
}

enum ImportFileType {
  CSV
  XLSX

  @@map("import_file_types")
}

enum ParkingFeePayer {
  OWNER // Owner of the parking space pays the parking management fee
  RENTER // Renter pays the parking management fee (most common)
  CUSTOM // Custom payer (use customPayeeResidentId or customPayeeName)

  @@map("parking_fee_payers")
}

enum UsageStatus {
  ACTIVE // Currently active usage
  EXPIRED // Usage period ended
  TERMINATED // Usage terminated early
  PENDING // Usage agreement signed but not started yet

  @@map("usage_statuses")
}

enum Role {
  ADMIN
  MEMBER
}

enum AccountType {
  BANK
  SAVINGS
  INVESTMENT
  CRYPTO
  VIRTUAL
}

enum BalanceType {
  AVAILABLE
  BOOKED
  EXPECTED
}

enum TimeDepositType {
  AVAILABLE
  WITHDRAWN
}

enum ConnectorEnv {
  DEVELOPMENT
  SANDBOX
  PRODUCTION
}

enum ConnectorStatus {
  ACTIVE
  BETA
  DEV
  INACTIVE
}

enum ConnectorType {
  DIRECT
  AGGREGATED
}

enum AssetType {
  REAL_ESTATE
  VEHICLE
  PRECIOUS_METALS
  OTHER
}

enum TransactionType {
  PURCHASE
  SALE
  MAINTENANCE
  IMPROVEMENT
  DEPRECIATION
  APPRECIATION
}

enum InvestmentType {
  STOCKS
  CRYPTO
  ETF
  OTHER
}

enum InvestmentTransactionType {
  BUY
  SELL
  DIVIDEND
  SPLIT
  MERGE
}

enum LiabilityType {
  MORTGAGE
  CREDIT_CARD
  CAR_LOAN
  STUDENT_LOAN
}

enum PaymentType {
  REGULAR
  EXTRA
  INTEREST
  PRINCIPAL
}

enum GoalType {
  EMERGENCY_FUND
  RETIREMENT
  DOWN_PAYMENT
  CUSTOM
}

enum TaskStatus {
  BACKLOG
  TODO
  IN_PROGRESS
  IN_REVIEW
  DONE
}

enum RunStatus {
  pending
  running
  error
  success
  timeout
  interrupted
}

enum ThreadStatus {
  idle
  busy
  interrupted
}

enum MultitaskStrategy {
  reject
  interrupt
  rollback
  enqueue
}

enum Visibility {
  PUBLIC
  PRIVATE
}

enum PaymentAccountType {
  REMITTER // 匯款人
  PAYEE // 受款人
  BOTH // 可作為匯款人或受款人
}

enum ReportType {
  MONTHLY
  ANNUAL
}

enum ContentVisibility {
  PUBLIC // 對所有人可見 (例如: 公開的年度報告)
  WORKSPACE_MEMBERS // 僅對報告所屬 Workspace 的成員可見
}

// if we need full reset : npx prisma db push --force-reset
