import { NextResponse } from 'next/server';
import { updateVehicleRegistration, deleteVehicleRegistration } from '@/actions/vehicleRegistrations/actions';

export async function PUT(req: Request, props: { params: Promise<{ workspaceId: string; id: string }> }) {
  try {
    const { workspaceId, id } = await props.params;
    const body = await req.json();
    const ok = await updateVehicleRegistration(workspaceId, id, body);
    return NextResponse.json({ success: ok });
  } catch (err: any) {
    return NextResponse.json({ success: false, error: err.message }, { status: 400 });
  }
}

export async function DELETE(req: Request, props: { params: Promise<{ workspaceId: string; id: string }> }) {
  try {
    const { workspaceId, id } = await props.params;
    const ok = await deleteVehicleRegistration(workspaceId, id);
    return NextResponse.json({ success: ok });
  } catch (err: any) {
    return NextResponse.json({ success: false, error: err.message }, { status: 400 });
  }
}
