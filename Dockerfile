# Stage 1: Install dependencies
FROM oven/bun:1.1.17-alpine AS deps
WORKDIR /app
COPY package.json bun.lockb ./
RUN bun install --frozen-lockfile

# Stage 2: Build the Next.js application
FROM oven/bun:1.1.17-alpine AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./
COPY . ./
ENV NEXT_TELEMETRY_DISABLED 1
RUN bun run build

# Stage 3: Production image
FROM oven/bun:1.1.17-alpine AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

# Create a non-root user
RUN addgroup --system --gid 1001 nextjs
RUN adduser --system --uid 1001 nextjs
USER nextjs

# Copy the standalone output and public assets
COPY --from=builder --chown=nextjs:nextjs /app/.next/standalone ./ 
COPY --from=builder --chown=nextjs:nextjs /app/public ./public

EXPOSE 3000
ENV PORT 3000

CMD ["bun", "start"]