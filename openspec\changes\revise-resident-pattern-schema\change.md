
# Revise ResidentPattern Schema

**Objective:** Revise the `ResidentPattern` schema to associate it with a `Workspace` and a `Member`, enabling access control based on workspace membership.

## Changes

The `ResidentPattern` model will be updated to include the following fields:

-   `workspaceId`: A foreign key to the `Workspace` model.
-   `memberId`: A foreign key to the `Member` model.

This change will mirror the relationship structure of the `Task` model, allowing for more granular control over `ResidentPattern` resources.

## Schema Updates

```prisma
model ResidentPattern {
  id          String   @id @default(cuid())
  name        String
  description String?
  pattern     <PERSON><PERSON>
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  memberId    String
  member      Member @relation(fields: [memberId], references: [id], onDelete: Cascade)

  @@index([workspaceId])
  @@index([memberId])
}
```
