import { prisma } from '@/lib/db';

async function main() {
  console.log('Seeding sample workspace and residents...');
  const workspace = await prisma.workspace.upsert({
    where: { id: 'sample-workspace' },
    update: {},
    create: { id: 'sample-workspace', name: 'Sample Workspace' },
  });

  const residents = [
    { residentId: '06-01-1', name: 'Resident A', workspaceId: workspace.id },
    { residentId: '06-02-1', name: 'Resident B', workspaceId: workspace.id },
    { residentId: '06-03-1', name: 'Resident C', workspaceId: workspace.id },
  ];

  for (const r of residents) {
    await prisma.resident.upsert({
      where: { residentId_workspaceId: { residentId: r.residentId, workspaceId: workspace.id } },
      update: { name: r.name },
      create: {
        residentId: r.residentId,
        name: r.name,
        address: '',
        workspaceId: workspace.id,
      },
    });
  }

  console.log('Seed complete');
}

main()
  .catch(e => { console.error(e); process.exit(1); })
  .finally(() => prisma.$disconnect());
