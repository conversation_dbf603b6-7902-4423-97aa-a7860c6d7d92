import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";

type ParkingSpacePayload = {
  spaceNumber: string;
  spaceType: 'CAR' | 'MOTORCYCLE' | 'BICYCLE';
  residentUnitId: string; // Changed from residentId to residentUnitId
  isActive?: boolean;
  notes?: string | null;
};

async function checkWorkspaceMembership(userId: string, workspaceId: string) {
  const member = await prisma.member.findFirst({
    where: {
      userId,
      workspaceId,
    },
  });
  return member;
}

// Helper function to get resident DB ID from residentUnitId (e.g., "06-01-1")
async function getResidentDbIdByUnitId(workspaceId: string, residentUnitId: string) {
  const resident = await prisma.resident.findFirst({
    where: {
      workspaceId,
      residentId: residentUnitId, // This is the '06-01-1' type ID
    },
    select: { id: true },
  });
  if (!resident) {
    throw new Error(`Resident with unit ID ${residentUnitId} not found in workspace ${workspaceId}`);
  }
  return resident.id; // Return the actual database ID
}

export async function getParkingSpaces(workspaceId: string) {
  const items = await prisma.parkingSpace.findMany({
    where: { workspaceId },
    include: { resident: { select: { name: true, residentId: true } } }, // Fetch residentUnitId for display
    orderBy: { spaceNumber: "asc" },
  });
  return items;
}

export async function createParkingSpace(
  workspaceId: string,
  payload: ParkingSpacePayload
) {
  const { userId } = await auth();
  if (!userId) throw new Error("Unauthorized");

  const member = await checkWorkspaceMembership(userId, workspaceId);
  if (!member) throw new Error("Unauthorized");

  const residentDbId = await getResidentDbIdByUnitId(workspaceId, payload.residentUnitId);

  const created = await prisma.parkingSpace.create({
    data: {
      spaceNumber: payload.spaceNumber,
      spaceType: payload.spaceType,
      residentId: residentDbId, // Use the resolved DB ID
      workspaceId,
      isActive: payload.isActive ?? true,
      notes: payload.notes || null,
    },
  });
  return created;
}

export async function updateParkingSpace(
  workspaceId: string,
  id: string,
  payload: Partial<ParkingSpacePayload>
) {
  const { userId } = await auth();
  if (!userId) throw new Error("Unauthorized");

  const member = await checkWorkspaceMembership(userId, workspaceId);
  if (!member) throw new Error("Unauthorized");

  let residentDbId: string | undefined = undefined;
  if (payload.residentUnitId !== undefined) {
    residentDbId = await getResidentDbIdByUnitId(workspaceId, payload.residentUnitId);
  }

  const updated = await prisma.parkingSpace.updateMany({
    where: { id, workspaceId },
    data: {
      spaceNumber: payload.spaceNumber ?? undefined,
      spaceType: payload.spaceType ?? undefined,
      residentId: residentDbId ?? undefined, // Use the resolved DB ID if available
      isActive: payload.isActive ?? undefined,
      notes: payload.notes ?? undefined,
      updatedAt: new Date(),
    },
  });
  return updated.count > 0;
}

export async function deleteParkingSpace(
  workspaceId: string,
  id: string
) {
  const { userId } = await auth();
  if (!userId) throw new Error("Unauthorized");

  const member = await checkWorkspaceMembership(userId, workspaceId);
  if (!member) throw new Error("Unauthorized");

  const del = await prisma.parkingSpace.deleteMany({
    where: { id, workspaceId },
  });
  return del.count > 0;
}
