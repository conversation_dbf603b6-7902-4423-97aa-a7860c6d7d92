"use server";

import { google } from "googleapis";
import { authenticateGoogleOAuth2 } from "@/lib/google/authenticate";
import { updateResidentAmount } from "./fill-autopay-resident-to-mgmt-sheets";

export type FailedAutopayRecord = {
  seq?: string; // 序號
  account?: string; // 帳號
  residentId?: string; // optional resident id if available from OCR
  debitCredit?: string; // 借貸別
  amount?: number; // 交易金額
  resultCode?: string; // 轉帳結果代碼
  raw?: string[];
};

type ProcessResult = {
  success: boolean;
  message: string;
  processedCount: number;
  deducted: Array<{ residentId: string; amount: number; seq?: string }>;
  unmappedAccounts: Array<{ account: string; seq?: string }>;
  duplicates?: Array<{ account: string; seq?: string }>;
};

function formatMonthYear(dateStr: string): string {
  if (!dateStr) return "";
  const s = String(dateStr).trim();
  // If input uses slashes like 'YYYY/MM/DD' or 'YYY/MM/DD' or 'YYYY/MM'
  const slashMatch = s.match(/^(\d{2,4})\/(\d{1,2})(?:\/(\d{1,2}))?$/);
  if (slashMatch) {
    let year = slashMatch[1];
    const month = slashMatch[2].padStart(2, '0');
    // Treat 2-3 digit year as ROC
    if (year.length <= 3) {
      const y = parseInt(year, 10);
      if (!isNaN(y)) year = String(y + 1911);
    }
    return `${year}/${month}`;
  }

  // Match 年月 format e.g. '114年11月' or '2025年11月'
  const match = s.match(/(\d{2,4})年(\d{1,2})月/);
  if (match) {
    let year = match[1];
    const month = match[2].padStart(2, '0');
    if (year.length <= 3) {
      const y = parseInt(year, 10);
      if (!isNaN(y)) year = String(y + 1911);
    }
    return `${year}/${month}`;
  }

  return "";
}

export async function processFailedAutopayRecords({
  records,
  processingDate, // e.g. '2025年08月'
}: {
  records: FailedAutopayRecord[];
  processingDate: string;
}): Promise<ProcessResult> {
  const spreadsheetIdForFailed = process.env.GOOGLE_AUTOPAY_RESIDENT_FAILED_SHEETS_ID;
  const mappingSpreadsheetId = process.env.GOOGLE_AUTOPAY_RESIDENT_ACCOUNT_SHEETS_ID;

  if (!spreadsheetIdForFailed) {
    return { success: false, message: 'Missing GOOGLE_AUTOPAY_RESIDENT_FAILED_SHEETS_ID', processedCount: 0, deducted: [], unmappedAccounts: [] };
  }

  if (!mappingSpreadsheetId) {
    return { success: false, message: 'Missing GOOGLE_AUTOPAY_RESIDENT_ACCOUNT_SHEETS_ID', processedCount: 0, deducted: [], unmappedAccounts: [] };
  }

  const auth = await authenticateGoogleOAuth2();
  if (!auth) {
    return { success: false, message: 'Google auth failed', processedCount: 0, deducted: [], unmappedAccounts: [] };
  }

  const sheets = google.sheets({ version: 'v4', auth });

  // Load account -> residentId mapping
  let accountMap = new Map<string, string>();
  try {
    const getResp = await sheets.spreadsheets.values.get({ spreadsheetId: mappingSpreadsheetId, range: 'A:E' });
    const values = getResp.data.values || [];
    // Expect header row and rows like: 序號, 帳號, 金額, 備註, 用戶代號
    for (let i = 1; i < values.length; i++) {
      const row = values[i];
      const account = String(row[1] || '').trim();
      const residentId = String(row[4] || '').trim();
      if (account) accountMap.set(account, residentId);
    }
  } catch (err) {
    console.error('Failed to read account mapping sheet', err);
    return { success: false, message: 'Failed to read account mapping sheet', processedCount: 0, deducted: [], unmappedAccounts: [] };
  }

  // Ensure the target sheet exists and append header if empty
  const sheetName = '自動轉帳失敗';
  try {
    // Try to read existing values
    const getFailed = await sheets.spreadsheets.values.get({ spreadsheetId: spreadsheetIdForFailed, range: `${sheetName}!A:Z` }).catch(() => null);

    if (!getFailed || !getFailed.data.values || getFailed.data.values.length === 0) {
      // Initialize header
      const header = [['processingDate', '序號', '帳號', '借貸別', '交易金額', '轉帳結果代碼']];
      await sheets.spreadsheets.values.append({ spreadsheetId: spreadsheetIdForFailed, range: `${sheetName}!A1`, valueInputOption: 'USER_ENTERED', requestBody: { values: header } }).catch(() => null);
    }
  } catch (err) {
    console.error('Failed to ensure failed sheet exists', err);
    // Continue; append will create sheet if allowed by API permissions
  }

  const monthYear = formatMonthYear(processingDate);

  const appendRows: any[][] = [];
  const deducted: Array<{ residentId: string; amount: number; seq?: string }> = [];
  const unmapped: Array<{ account: string; seq?: string }> = [];
  const duplicates: Array<{ account: string; seq?: string }> = [];

  // Build a set of existing records to prevent duplicates. Key uses processingDate|seq|account|amount
  const existingKeys = new Set<string>();
  try {
    const existingResp = await sheets.spreadsheets.values.get({ spreadsheetId: spreadsheetIdForFailed, range: `${sheetName}!A2:F` }).catch(() => null);
    const existingVals = existingResp && existingResp.data && existingResp.data.values ? existingResp.data.values : [];
    for (const row of existingVals) {
      const pd = String(row[0] || '').trim();
      const s = String(row[1] || '').trim();
      const acct = String(row[2] || '').trim();
      const amt = parseFloat(String(row[4] || '0')) || 0;
      const key = `${pd}|${s}|${acct}|${amt}`;
      existingKeys.add(key);
    }
  } catch (err) {
    console.warn('Could not read existing failed sheet rows for dedupe check', err);
  }

  for (const rec of records) {
    const seq = rec.seq || '';
    const account = (rec.account || '').trim();
    const residentIdFromRec = (rec.residentId || '').trim();
    const debitCredit = rec.debitCredit || '';
    const amount = typeof rec.amount === 'number' ? rec.amount : parseFloat(String(rec.amount || '').replace(/,/g, '')) || 0;
    const resultCode = rec.resultCode || '';

    const key = `${processingDate || ''}|${seq}|${account}|${amount}`;
    if (existingKeys.has(key)) {
      // duplicate -> skip append and skip deduction
      duplicates.push({ account, seq });
      continue;
    }

    appendRows.push([processingDate || '', seq, account, debitCredit, amount, resultCode]);

    // If OCR already provided residentId, use it directly
    const residentId = residentIdFromRec || accountMap.get(account);
    if (residentId) {
      // Deduct the amount from the management sheet (negative delta)
      console.log("residentId", residentId);
      try {
        const res = await updateResidentAmount({ residentId, monthYear, amountDelta: -amount });
        if (res.success) {
          deducted.push({ residentId, amount: -amount, seq });
        } else {
          console.warn('Failed to deduct amount for', residentId, res.message);
        }
      } catch (err) {
        console.error('Error updating resident amount', err);
      }
    } else {
      unmapped.push({ account, seq });
    }
  }

  // Append all failed rows into sheet
  try {
    if (appendRows.length > 0) {
      await sheets.spreadsheets.values.append({
        spreadsheetId: spreadsheetIdForFailed,
        range: `${sheetName}!A:Z`,
        valueInputOption: 'USER_ENTERED',
        requestBody: { values: appendRows },
      });
    }
  } catch (err) {
    console.error('Failed to append failed autopay rows', err);
    return { success: false, message: 'Failed to append failed autopay rows', processedCount: appendRows.length, deducted, unmappedAccounts: unmapped };
  }

  return {
    success: true,
    message: `Processed ${appendRows.length} failed records. Deducted for ${deducted.length} residents, ${unmapped.length} unmapped accounts, ${duplicates.length} duplicates skipped.`,
    processedCount: appendRows.length,
    deducted,
    unmappedAccounts: unmapped,
    duplicates,
  };
}
