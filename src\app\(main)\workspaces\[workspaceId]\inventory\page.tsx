"use client";

import React, { useState, useMemo, use } from "react";
import {
  Plus,
  Minus,
  Package,
  Users,
  Calendar,
  FileText,
  RefreshCw,
  Edit,
  Trash2,
  Printer,
} from "lucide-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getInventory,
  getSalesHistory,
  processSale as apiProcessSale,
  updateInventoryStock as apiUpdateStock,
  isSuperUser as apiIsSuperUser,
  deleteSale as apiDeleteSale,
  updateSale as apiUpdateSale,
} from "@/actions/inventory/actions";
import {
  ItemType,
  ItemTypeDisplay,
  SaleRecord,
  SaleFormData,
  InventoryData,
  getItemTypeDisplayName,
} from "@/types/inventory";
import { Loading } from "@/components/ui/loader";
import { toast } from "sonner";
import * as XLSX from 'xlsx';
import ResidentSearch from "@/components/resident-search";

// Helper function to export data to XLSX
const exportToXLSX = (data: any[], sheetName: string, fileName: string) => {
  const ws = XLSX.utils.json_to_sheet(data);
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, sheetName);
  XLSX.writeFile(wb, fileName);
};

// Specialized function for Inventory XLSX export
const exportInventoryDataToXLSX = (inventoryData: InventoryData[]) => {
  const header = ["類型", "數量", "單價"];
  const data = inventoryData.map(item => ({
    "類型": getItemTypeDisplayName(item.itemType),
    "數量": item.current,
    "單價": item.price
  }));
  const ws = XLSX.utils.json_to_sheet(data, { header: header });
  XLSX.utils.sheet_add_aoa(ws, [["磁扣/搖控器庫存明細"]], { origin: "A1" });
  XLSX.utils.sheet_add_aoa(ws, [[]], { origin: "A2" }); // Empty row for spacing
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, "庫存明細");
  XLSX.writeFile(wb, "磁扣搖控器庫存明細.xlsx");
};

// Specialized function for Sales History XLSX export
const exportSalesDataToXLSX = (salesHistory: SaleRecord[]) => {
  const header = ["日期", "購買人姓名", "地址", "收據編號", "收款人", "項目", "數量", "單價", "總額"];
  const data = salesHistory.flatMap(sale => {
    return sale.saleItems.map(item => ({
      "日期": new Date(sale.date).toLocaleDateString(),
      "購買人姓名": sale.purchaserName,
      "地址": sale.address,
      "收據編號": sale.receiptNo || "",
      "收款人": sale.recipient || "",
      "項目": ItemTypeDisplay[item.itemType],
      "數量": item.quantity,
      "單價": item.unitPrice,
      "總額": item.lineTotal
    }));
  });
  const ws = XLSX.utils.json_to_sheet(data, { header: header });
  XLSX.utils.sheet_add_aoa(ws, [["磁扣/搖控器銷售紀錄"]], { origin: "A1" });
  XLSX.utils.sheet_add_aoa(ws, [[]], { origin: "A2" }); // Empty row for spacing
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, "銷售記錄");
  XLSX.writeFile(wb, "磁扣搖控器銷售記錄.xlsx");
};

// Helper function to print a specific section
const printSection = (sectionId: string) => {
  const section = document.getElementById(sectionId);
  if (section) {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write('<html><head><title>Print</title>');
      // Copy styles from the current document
      Array.from(document.styleSheets).forEach(styleSheet => {
        try {
          if (styleSheet.href) {
            printWindow.document.write(`<link rel="stylesheet" href="${styleSheet.href}">`);
          } else if (styleSheet.cssRules) {
            const style = document.createElement('style');
            Array.from(styleSheet.cssRules).forEach(rule => {
              style.appendChild(document.createTextNode(rule.cssText));
            });
            printWindow.document.head.appendChild(style);
          }
        } catch (e) {
          console.warn("Could not copy stylesheet:", e);
        }
      });
      printWindow.document.write('</head><body>');
      printWindow.document.write(section.innerHTML);
      printWindow.document.write('</body></html>');
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
      printWindow.close();
    }
  }
};

type InventoryState = Record<ItemType, { current: number; price: number }>;

const EditSaleModal: React.FC<{
  sale: SaleRecord;
  inventory: InventoryState;
  onClose: () => void;
  onSave: (data: SaleFormData) => Promise<void>;
  isSaving: boolean;
}> = ({ sale, inventory, onClose, onSave, isSaving }) => {
  const [form, setForm] = useState<SaleFormData>(() => {
    const saleItem = sale.saleItems[0];
    return {
      date: new Date(sale.date).toISOString().split("T")[0],
      purchaserName: sale.purchaserName || "",
      purchaserType: sale.purchaserType || "",
      address: sale.address || "",
      receiptNo: sale.receiptNo || "",
      itemType: saleItem.itemType,
      quantity: saleItem.quantity,
      unitPrice: saleItem.unitPrice,
      recipient: sale.recipient || "",
    };
  });

  const handleChange = <K extends keyof SaleFormData>(
    field: K,
    value: SaleFormData[K]
  ) => {
    setForm((prev) => {
      const updated = { ...prev, [field]: value };
      if (field === "itemType") {
        updated.unitPrice = inventory[value as ItemType]?.price || 0;
      }
      return updated;
    });
  };

  const itemTypes = Object.keys(inventory) as ItemType[];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl">
        <h2 className="text-2xl font-bold mb-4">Edit Sale</h2>
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              購買日期
            </label>
            <input
              type="date"
              value={form.date}
              onChange={(e) => handleChange("date", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              購買人姓名 *
            </label>
            <input
              type="text"
              value={form.purchaserName}
              onChange={(e) => handleChange("purchaserName", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              購買類型
            </label>
            <select
              value={form.purchaserType || "OWNER"}
              onChange={(e) => handleChange("purchaserType", e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="OWNER">屋主 (OWNER)</option>
              <option value="TENANT">承租人 (TENANT)</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              地址 *
            </label>
            <input
              type="text"
              value={form.address}
              onChange={(e) => handleChange("address", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              購買項目
            </label>
            <select
              value={form.itemType}
              onChange={(e) =>
                handleChange("itemType", e.target.value as ItemType)
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              {itemTypes.map((item) => (
                <option key={item} value={item}>
                  {ItemTypeDisplay[item]}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              購買數量
            </label>
            <input
              type="number"
              min="1"
              value={form.quantity}
              onChange={(e) =>
                handleChange("quantity", parseInt(e.target.value) || 1)
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              單價
            </label>
            <input
              type="number"
              min="0"
              value={form.unitPrice}
              onChange={(e) =>
                handleChange("unitPrice", parseInt(e.target.value) || 0)
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              收據編號
            </label>
            <input
              type="text"
              value={form.receiptNo}
              onChange={(e) => handleChange("receiptNo", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              收款人
            </label>
            <input
              type="text"
              value={form.recipient}
              onChange={(e) => handleChange("recipient", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
        </div>
        <div className="mt-6 flex justify-end gap-4">
          <button
            onClick={onClose}
            disabled={isSaving}
            className="px-4 py-2 bg-gray-200 rounded-md disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={() => onSave(form)}
            disabled={isSaving}
            className="px-4 py-2 bg-blue-600 text-white rounded-md disabled:opacity-50"
          >
            {isSaving ? "Saving..." : "Save Changes"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default function MagneticCardInventoryPage(props: {
  params: Promise<{ workspaceId: string }>;
}) {
  const params = use(props.params);
  const queryClient = useQueryClient();
  const { workspaceId } = params;
  const [editingSale, setEditingSale] = useState<SaleRecord | null>(null);

  const { data: inventoryData, isLoading: isInventoryLoading } = useQuery({
    queryKey: ["inventory", workspaceId],
    queryFn: () => getInventory(workspaceId),
  });
  const { data: salesHistory = [], isLoading: isSalesLoading } = useQuery({
    queryKey: ["salesHistory", workspaceId],
    queryFn: () => getSalesHistory(workspaceId),
  });
  const { data: isSuperUser = false } = useQuery({
    queryKey: ["isSuperUser", workspaceId],
    queryFn: () => apiIsSuperUser(workspaceId),
  });

  const inventoryMap = useMemo(() => {
    if (!inventoryData) return {} as InventoryState;
    return inventoryData.reduce((acc, item) => {
      acc[item.itemType] = { current: item.current, price: item.price };
      return acc;
    }, {} as InventoryState);
  }, [inventoryData]);

  const totalSalesAmount = useMemo(() => {
    return salesHistory.reduce((sum, sale) => sum + sale.totalAmount, 0);
  }, [salesHistory]);

  const [saleForm, setSaleForm] = useState<SaleFormData>({
    date: new Date().toISOString().split("T")[0],
    purchaserName: "",
    address: "",
    receiptNo: "",
    itemType: ItemType.A_BUILDING_CARD,
    quantity: 1,
    unitPrice: 200,
    recipient: "",
    purchaserType: "",
  });

  const handleMutationSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ["inventory", workspaceId] });
    queryClient.invalidateQueries({ queryKey: ["salesHistory", workspaceId] });
  };

  const processSaleMutation = useMutation({
    mutationFn: (saleData: SaleFormData) => apiProcessSale(workspaceId, saleData),
    onSuccess: () => {
      handleMutationSuccess();
      toast.success("銷售記錄已完成！");
      setSaleForm({
        date: new Date().toISOString().split("T")[0],
        purchaserName: "",
        address: "",
        receiptNo: "",
        itemType: ItemType.A_BUILDING_CARD,
        quantity: 1,
        unitPrice: inventoryMap[ItemType.A_BUILDING_CARD]?.price || 200,
        recipient: "",
      });
    },
    onError: (error: Error) => toast.error(`處理失敗: ${error.message}`),
  });

  const updateStockMutation = useMutation({
    mutationFn: (variables: { itemType: ItemType; amount: number }) =>
      apiUpdateStock(workspaceId, variables.itemType, variables.amount),
    onSuccess: handleMutationSuccess,
    onError: (error: Error) => toast.error(`更新庫存失敗: ${error.message}`),
  });
  const deleteSaleMutation = useMutation({
    mutationFn: (saleId: string) => apiDeleteSale(workspaceId, saleId),
    onSuccess: () => {
      handleMutationSuccess();
      toast.success("Sale deleted successfully.");
    },
    onError: (error: Error) =>
      toast.error(`Failed to delete sale: ${error.message}`),
  });
  const updateSaleMutation = useMutation({
    mutationFn: (variables: { saleId: string; data: SaleFormData }) =>
      apiUpdateSale(workspaceId, variables.saleId, variables.data),
    onSuccess: () => {
      handleMutationSuccess();
      setEditingSale(null);
      toast.success("Sale updated successfully.");
    },
    onError: (error: Error) =>
      toast.error(`Failed to update sale: ${error.message}`),
  });

  const handleFormChange = <K extends keyof SaleFormData>(
    field: K,
    value: SaleFormData[K]
  ): void => {
    setSaleForm((prev) => {
      const updated = { ...prev, [field]: value };
      if (field === "itemType") {
        updated.unitPrice = inventoryMap[value as ItemType]?.price || 0;
      }
      return updated;
    });
  };

  const processSale = () => {
    const { itemType, quantity, purchaserName, address } = saleForm;
    if (!purchaserName.trim() || !address.trim())
      return toast.error("請填寫購買人姓名和地址");
    if (!inventoryMap[itemType] || inventoryMap[itemType].current < quantity)
      return toast.error(
        `庫存不足！目前${ItemTypeDisplay[itemType]}剩餘：${inventoryMap[itemType]?.current || 0}個`
      );
    processSaleMutation.mutate(saleForm);
  };

  const handleDelete = (saleId: string) => {
    if (
      window.confirm(
        "Are you sure you want to delete this sale record? This will also restock the inventory. This action cannot be undone."
      )
    ) {
      deleteSaleMutation.mutate(saleId);
    }
  };

  const handleUpdateSale = async (data: SaleFormData) => {
    if (!editingSale) return;
    updateSaleMutation.mutate({ saleId: editingSale.id, data });
  };

  const getTotalValue = (): number =>
    Object.values(inventoryMap).reduce(
      (total, data) => total + data.current * data.price,
      0
    );
  const getInventoryEntries = (): [
    ItemType,
    { current: number; price: number },
  ][] =>
    Object.entries(inventoryMap) as [
      ItemType,
      { current: number; price: number },
    ][];
  const getItemTypes = (): ItemType[] =>
    Object.keys(inventoryMap) as ItemType[];

  if (isInventoryLoading || isSalesLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loading />
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 bg-gray-50 min-h-screen">
      {editingSale && (
        <EditSaleModal
          sale={editingSale}
          inventory={inventoryMap}
          onClose={() => setEditingSale(null)}
          onSave={handleUpdateSale}
          isSaving={updateSaleMutation.isPending}
        />
      )}
      <div className="bg-white rounded-lg shadow-lg p-6 mb-6" id="inventory-section">
        <div className="flex justify-between items-center mb-2">
          <h1 className="text-3xl font-bold text-gray-800">
            僑星福華社區磁扣庫存管理系統
          </h1>
          <div className="flex gap-2">
            <button
              onClick={() => exportInventoryDataToXLSX(inventoryData || [])}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors"
              title="下載庫存明細為XLSX"
            >
              <FileText className="text-gray-600" />
            </button>
            <button
              onClick={() => printSection("inventory-section")}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors"
              title="列印庫存明細"
            >
              <Printer className="text-gray-600" />
            </button>
            <button
              onClick={() => queryClient.invalidateQueries({ queryKey: ["inventory", workspaceId] })}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors"
            >
              <RefreshCw
                className={`text-gray-600 ${isInventoryLoading || isSalesLoading ? "animate-spin" : ""}`}
              />
            </button>
          </div>
        </div>
        <p className="text-center text-gray-600 mb-6">
          Magnetic Card & Remote Control Inventory Management
        </p>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          {getInventoryEntries().map(([item, data]) => (
            <div
              key={item}
              className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200"
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-bold text-gray-800">
                  {ItemTypeDisplay[item]}
                </h3>
                <Package className="text-blue-600" size={20} />
              </div>
              <div className="text-2xl font-bold text-blue-600 mb-2">
                {data.current}
              </div>
              <div className="text-sm text-gray-600 mb-3">
                單價: ${data.price.toLocaleString()}
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() =>
                    updateStockMutation.mutate({ itemType: item, amount: 1 })
                  }
                  className="flex-1 bg-green-500 text-white px-2 py-1 rounded text-sm hover:bg-green-600 flex items-center justify-center gap-1 transition-colors"
                >
                  <Plus size={14} /> 補貨
                </button>
                <button
                  onClick={() =>
                    data.current > 0 &&
                    updateStockMutation.mutate({ itemType: item, amount: -1 })
                  }
                  disabled={data.current === 0}
                  className="flex-1 bg-red-500 text-white px-2 py-1 rounded text-sm hover:bg-red-600 flex items-center justify-center gap-1 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                >
                  <Minus size={14} /> 調整
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="bg-gradient-to-r from-green-100 to-blue-100 rounded-lg p-4 mb-8">
          <div className="flex items-center justify-between">
            <span className="text-lg font-semibold text-gray-800">
              總庫存價值
            </span>
            <span className="text-2xl font-bold text-green-600">
              ${getTotalValue().toLocaleString()}
            </span>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-4 flex items-center gap-2">
          <Users className="text-blue-600" />
          購買登記
        </h2>
        <div className="grid md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                購買日期
              </label>
              <input
                type="date"
                value={saleForm.date}
                onChange={(e) => handleFormChange("date", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                購買人姓名 *
              </label>
              <input
                type="text"
                value={saleForm.purchaserName}
                onChange={(e) =>
                  handleFormChange("purchaserName", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="請輸入購買人姓名"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                購買類型
              </label>
              <select
                value={saleForm.purchaserType || "OWNER"}
                onChange={(e) => handleFormChange("purchaserType", e.target.value as any)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="OWNER">屋主 (OWNER)</option>
                <option value="TENANT">承租人 (TENANT)</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                地址 *
              </label>
              {/* Replace free-text address with searchable resident selector */}
              <React.Suspense fallback={<input
                type="text"
                value={saleForm.address}
                onChange={(e) => handleFormChange("address", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="例: 06-01-1"
                required
              />}>
                {/* @ts-ignore */}
                <ResidentSearch
                  workspaceId={workspaceId}
                  value={saleForm.address}
                  onChange={(v: string) => handleFormChange('address', v)}
                  placeholder="例: 06-01-1"
                />
              </React.Suspense>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                收據編號
              </label>
              <input
                type="tel"
                value={saleForm.receiptNo}
                onChange={(e) => handleFormChange("receiptNo", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="請輸入收據編號"
              />
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                購買項目
              </label>
              <select
                value={saleForm.itemType}
                onChange={(e) =>
                  handleFormChange("itemType", e.target.value as ItemType)
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                {getItemTypes().map((item) => (
                  <option key={item} value={item}>
                    {ItemTypeDisplay[item]} (庫存:{" "}
                    {inventoryMap[item]?.current || 0})
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                購買數量
              </label>
              <input
                type="number"
                min="1"
                max={inventoryMap[saleForm.itemType]?.current || 0}
                value={saleForm.quantity}
                onChange={(e) =>
                  handleFormChange(
                    "quantity",
                    Math.max(1, parseInt(e.target.value) || 1)
                  )
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                單價
              </label>
              <input
                type="number"
                min="0"
                value={saleForm.unitPrice}
                onChange={(e) =>
                  handleFormChange(
                    "unitPrice",
                    Math.max(0, parseInt(e.target.value) || 0)
                  )
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                收款人 *
              </label>
              <textarea
                value={saleForm.recipient}
                onChange={(e) => handleFormChange("recipient", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md resize-none"
                rows={2}
                placeholder="收款人簽章"
                required
              />
            </div>
          </div>
        </div>
        <div className="mt-6 flex justify-between items-center">
          <div className="text-lg font-semibold text-gray-800">
            總金額:{" "}
            <span className="text-green-600">
              ${(saleForm.quantity * saleForm.unitPrice).toLocaleString()}
            </span>
          </div>
          <button
            onClick={processSale}
            disabled={processSaleMutation.isPending}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2 transition-colors disabled:bg-gray-400"
          >
            <FileText size={20} />
            {processSaleMutation.isPending ? "Processing..." : "確認購買"}
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-lg p-6" id="sales-history-section">
        <h2 className="text-2xl font-bold text-gray-800 mb-4 flex items-center gap-2">
          <Calendar className="text-green-600" />
          銷售記錄 ({salesHistory.length})
        </h2>
        <div className="flex justify-end gap-2 mb-4">
          <button
            onClick={() => exportSalesDataToXLSX(salesHistory || [])}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
            title="下載銷售記錄為XLSX"
          >
            <FileText className="text-gray-600" />
          </button>
          <button
            onClick={() => printSection("sales-history-section")}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
            title="列印銷售記錄"
          >
            <Printer className="text-gray-600" />
          </button>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full table-auto">
            <thead>
              <tr className="bg-gray-100">
                <th className="px-4 py-2 text-left font-medium text-gray-700">
                  日期
                </th>
                <th className="px-4 py-2 text-left font-medium text-gray-700">
                  購買人姓名
                </th>
                <th className="px-4 py-2 text-left font-medium text-gray-700">
                  購買類型
                </th>
                <th className="px-4 py-2 text-left font-medium text-gray-700">
                  地址
                </th>
                <th className="px-4 py-2 text-left font-medium text-gray-700">
                  收據編號
                </th>
                <th className="px-4 py-2 text-left font-medium text-gray-700">
                  收款人
                </th>
                <th className="px-4 py-2 text-left font-medium text-gray-700">
                  項目
                </th>
                <th className="px-4 py-2 text-left font-medium text-gray-700">
                  總額
                </th>
                {isSuperUser && (
                  <th className="px-4 py-2 text-left font-medium text-gray-700">
                    Actions
                  </th>
                )}
              </tr>
            </thead>
            <tbody>
              {(salesHistory as SaleRecord[]).map((sale) => (
                <tr key={sale.id} className={"bg-gray-50"}>
                  <td className="px-4 py-2 text-sm">
                    {new Date(sale.date).toLocaleDateString()}
                  </td>
                  <td className="px-4 py-2 text-sm font-medium">
                    {sale.purchaserName}
                  </td>
                  <td className="px-4 py-2 text-sm">
                    {sale.purchaserType === "OWNER"
                      ? "屋主"
                      : sale.purchaserType === "TENANT"
                      ? "承租人"
                      : "自動判斷"}
                  </td>
                  <td className="px-4 py-2 text-sm">{sale.address}</td>
                  <td className="px-4 py-2 text-sm">{sale.receiptNo}</td>
                  <td className="px-4 py-2 text-sm">{sale.recipient}</td>
                  <td className="px-4 py-2 text-sm">
                    {sale.saleItems
                      .map((i) => ItemTypeDisplay[i.itemType])
                      .join(", ")}
                  </td>
                  <td className="px-4 py-2 text-sm font-medium text-green-600">
                    ${sale.totalAmount.toLocaleString()}
                  </td>
                  {isSuperUser && (
                    <td className="px-4 py-2 text-sm flex items-center">
                      <button
                        onClick={() => setEditingSale(sale)}
                        disabled={
                          deleteSaleMutation.isPending ||
                          updateSaleMutation.isPending
                        }
                        className="p-1 text-blue-600 hover:text-blue-800 disabled:opacity-50"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => handleDelete(sale.id)}
                        disabled={
                          deleteSaleMutation.isPending ||
                          updateSaleMutation.isPending
                        }
                        className="p-1 text-red-600 hover:text-red-800 ml-2 disabled:opacity-50"
                      >
                        <Trash2 size={16} />
                      </button>
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <div className="mt-4 flex justify-end items-center">
          <span className="text-lg font-semibold text-gray-800">
            總銷售金額:
          </span>
          <span className="text-2xl font-bold text-green-600 ml-2">
            ${totalSalesAmount.toLocaleString()}
          </span>
        </div>
      </div>
    </div>
  );
};
