import { NextResponse } from 'next/server';
import { updateFeeConfiguration, deleteFeeConfiguration } from '@/actions/fees/actions';

export async function PUT(req: Request, props: { params: Promise<{ workspaceId: string; id: string }> }) {
  const { workspaceId, id } = await props.params;
  const body = await req.json();
  const payload = {
    feeType: body.feeType,
    unitPrice: body.unitPrice,
    description: body.description,
    effectiveFrom: body.effectiveFrom,
    effectiveTo: body.effectiveTo,
    isActive: body.isActive,
  };
  const res = await updateFeeConfiguration(workspaceId, id, payload);
  return NextResponse.json(res);
}

export async function DELETE(req: Request, props: { params: Promise<{ workspaceId: string; id: string }> }) {
  const { workspaceId, id } = await props.params;
  const res = await deleteFeeConfiguration(workspaceId, id);
  return NextResponse.json(res);
}
