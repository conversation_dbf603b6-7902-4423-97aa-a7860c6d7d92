import { NextResponse } from 'next/server';
import { getVehicleRegistrations, createVehicleRegistration } from '@/actions/vehicleRegistrations/actions';

export async function GET(req: Request, props: { params: Promise<{ workspaceId: string }> }) {
  try {
    const { workspaceId } = await props.params;
    const items = await getVehicleRegistrations(workspaceId);
    return NextResponse.json({ success: true, data: items });
  } catch (err: any) {
    return NextResponse.json({ success: false, error: err.message }, { status: 500 });
  }
}

export async function POST(req: Request, props: { params: Promise<{ workspaceId: string }> }) {
  try {
    const { workspaceId } = await props.params;
    const body = await req.json();
    const created = await createVehicleRegistration(workspaceId, body);
    return NextResponse.json({ success: true, data: created });
  } catch (err: any) {
    return NextResponse.json({ success: false, error: err.message }, { status: 400 });
  }
}
