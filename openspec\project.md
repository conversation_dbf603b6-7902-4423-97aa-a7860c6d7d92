# Project Context

## Purpose
Provide management fee income and financial expenditure management for residential communities.

## Tech Stack
- Next.js (React, TypeScript)
- Tailwind CSS
- Bun (Package Manager)
- Convex (Backend/Database)
- Prisma (ORM)
- Inngest (Serverless Functions)
- <PERSON><PERSON><PERSON> (Deployment)

## Project Conventions

### Code Style
- **Language**: TypeScript
- **Formatting**: <PERSON><PERSON><PERSON> (inferred from common project setups)
- **Linting**: ESLint (inferred from common project setups)
- **Naming Conventions**: Follow standard TypeScript/JavaScript conventions (e.g., camelCase for variables and functions, PascalCase for components and types).

### Architecture Patterns
- **Frontend**: Next.js for React-based UI, utilizing its file-system based routing and API routes.
- **Backend**: Convex for real-time database and serverless functions.
- **Event-driven**: Inngest for handling background jobs and event-driven workflows.

### Testing Strategy
- **Unit Testing**: Jest or Vitest for individual functions and components.
- **Integration Testing**: React Testing Library for testing component interactions.
- **End-to-End Testing**: Play<PERSON> or <PERSON><PERSON> for full application flow testing.
(Please confirm if this strategy aligns with your preferences or if you have specific tools in mind.)

### Git Workflow
- **Basic Workflow**: `git add .`, `git commit -m ""`, `git push source main`

## Domain Context
Financial and accounting principles.

## Important Constraints
- **Security**: Strict adherence to data security best practices for sensitive financial and personal information.
- **Data Integrity**: Ensuring accuracy, consistency, and reliability of all financial data.
- **Regulatory Compliance**: Adherence to relevant financial and accounting regulations and data privacy laws.
- **Performance**: The system must be performant and responsive for financial transactions and reporting.
(Please review and confirm if these constraints are appropriate or if you have others in mind.)

## External Dependencies
- Convex (Backend as a Service, Database, Realtime)
- Inngest (Serverless Functions, Event-driven platform)
- Vercel (Deployment, Hosting)
