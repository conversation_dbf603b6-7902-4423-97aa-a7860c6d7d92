## ADDED Requirements
### Requirement: Manage Resident Patterns
The system SHALL allow users to manage resident-to-transaction mapping patterns.

#### Scenario: Create a new resident pattern
- **GIVEN** a user is on the resident pattern management page
- **WHEN** the user fills out the form with a resident identifier and one or more regex patterns and submits it
- **THEN** a new resident pattern is created and appears in the list of patterns.

#### Scenario: View resident patterns
- **GIVEN** a user has created resident patterns
- **WHEN** the user navigates to the resident pattern management page
- **THEN** the system SHALL display a list of all their resident patterns.

#### Scenario: Update a resident pattern
- **GIVEN** a user is on the resident pattern management page
- **WHEN** the user selects a pattern to edit, changes its values, and saves it
- **THEN** the resident pattern SHALL be updated with the new values.

#### Scenario: Delete a resident pattern
- **GIVEN** a user is on the resident pattern management page
- **WHEN** the user selects a pattern and confirms deletion
- **THEN** the resident pattern SHALL be removed from the system.

### Requirement: Use Database-backed Resident Patterns
The system SHALL use the user-configured resident patterns from the database to identify residents from transaction data.

#### Scenario: Process deposits with database patterns
- **GIVEN** a user has configured resident patterns in the database
- **WHEN** a deposit processing job is run
- **THEN** the system SHALL use the patterns from the database to match transactions to residents, instead of the hardcoded `RESIDENT_PATTERNS` map.
