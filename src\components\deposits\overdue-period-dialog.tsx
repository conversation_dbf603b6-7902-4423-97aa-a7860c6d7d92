"use client";
import { useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "sonner";

interface OverduePeriodDialogProps {
  overdueList: Array<{
    id: string;
    amount: string;
    months: string;
  }>;  
  overdueMonthThreshold: number;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function OverduePeriodDialog({
  overdueList,  
  overdueMonthThreshold,
  open,
  onOpenChange,
}: OverduePeriodDialogProps) {

  const totalOverdueAmount = useMemo(() => {
    return overdueList.reduce((sum, item) => {
      const amount = parseFloat(item.amount.replace(/,/g, ""));
      return sum + (isNaN(amount) ? 0 : amount);
    }, 0);
  }, [overdueList]);

  const totalOverdueMonths = useMemo(() => {
    return overdueList.reduce((sum, item) => {
      const months = parseFloat(item.months);
      return sum + (isNaN(months) ? 0 : months);
    }, 0);
  }, [overdueList]);


  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>欠費 {overdueMonthThreshold} 個月以上住戶</DialogTitle>
        </DialogHeader>
        <div className="flex justify-between items-center mb-4">
          <div className="text-sm text-muted-foreground font-bold">
            共 {overdueList.length} 筆
          </div>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={async () => {
                const header = ["序號", "住戶編號", "累計未繳金額", "期數"];
                const rows = overdueList.map((item, idx) => [
                  idx + 1,
                  item.id,
                  item.amount,
                  item.months,
                ]);
                const text = [header, ...rows]
                  .map((r) => r.join("\t"))
                  .join("\n");

                try {
                  await navigator.clipboard.writeText(text);
                  toast.success("已複製資料！");
                } catch {
                  toast.error("複製失敗，請手動選取複製。");
                }
              }}
            >
              複製資料
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                // Create a new window for printing
                const printWindow = window.open("", "_blank");

                if (printWindow) {
                  printWindow.document.write(`
                    <html>
                      <head>
                      <title>僑星福華社區_${new Date().getFullYear() - 1911}年${new Date().getMonth() + 1}月${new Date().getDate()}日_欠費 ${overdueMonthThreshold} 個月以上住戶</title>
                      <style>
                      body { font-family: Arial, sans-serif; margin: 20px; }
                      table { 
                        border-collapse: collapse; 
                        width: 100%; 
                        font-size: 1.5rem; /* 24px - equivalent to text-2xl */
                      }
                      th, td { 
                        border: 1px solid #070707ff; 
                        padding: 4px; 
                        text-align: center;
                        font-size: 1.5rem; /* 24px */
                      }
                      th { background-color: #f2f2f2; font-size: 1.5rem; font-weight: bold; }
                      .pt-2 { padding-top: 10px; }
                      .py-2 { padding-top: 10px; padding-bottom: 10px; }
                      .text-right { text-align: right; }
                      .text-center { text-align: center; }
                      .text-4xl { 
                        font-size: 2.5rem; 
                        line-height: 3rem; /* 48px */
                      }
                      .text-3xl { 
                        font-size: 2rem; /* 32px */
                        line-height: 2.5rem; /* 40px */
                      }
                      .text-table-title { 
                        font-size: 1.75rem;
                        line-height: 1.95rem;
                      }
                      .text-2xl { 
                        font-size: 1.5rem; /* 24px */ 
                        line-height: 1.75rem; /* 28px */
                      }
                      .text-xl { 
                        font-size: 1.25rem; /* 20px */
                        line-height: 1.75rem; /* 28px */
                      }
                      .text-lg { 
                        font-size: 1.125rem; /* 18px */
                        line-height: 1.75rem; /* 28px */
                      }
                      .text-sm { 
                        font-size: 0.875rem; /* 14px */
                        line-height: 1.25rem; /* 20px */
                      }
                      .text-red { color: red; }
                      .font-bold { font-weight: bold; }
                      .underline { text-decoration: underline; }
                      .bg-muted { background-color: #f8f9fa; }
                      h1 { margin-bottom: 20px; text-3xl}
                      .summary { margin-bottom: 10px; }
                      /* Add fixed width for 序號 column */
                      th:first-child, td:first-child { 
                      width: 4rem;
                      min-width: 4rem;
                      max-width: 4rem;
                      }
                      .tracking-wider { letter-spacing: 0.2em; }
                      </style>
                      </head>
                      <body>
                      <h1 class="text-4xl text-center font-bold underline tracking-wider">繳 費 提 醒</h1>
                      <div class="pt-2 text-2xl font-bold">
                      截至 ${new Date().getFullYear()} 年 ${new Date().getMonth()} 月 ${new Date(new Date().getFullYear(), new Date().getMonth(), 0).getDate()} 日止，社區管理費逾期未繳名單如下，惠請住戶儘快繳納。
                      </div>
                      <div class="text-2xl text-red font-bold">※如有筆誤，請攜帶繳費證明更正。</div>
                      <div class="py-2 text-center text-2xl font-bold tracking-wider">【管理費逾期未繳納名單】</div>
                      <table class="table">
                      <thead>
                      <tr class="bg-muted">
                      <th>序號</th>
                      <th>住戶編號</th>
                      <th>累計未繳金額</th>
                      <th>期數</th>
                      </tr>
                      </thead>
                      <tbody>
                      ${overdueList
                        .map(
                          (item, idx) => `
                      <tr>
                      <td class="td text-center font-bold">${idx + 1}</td>
                      <td class="td text-center font-bold">${item.id}</td>
                      <td class="td text-right font-bold">${item.amount}</td>
                      <td class="td text-right font-bold">${item.months}</td>
                      </tr>
                      `
                        )
                        .join("")}
                      </tbody>
                      <tfoot>
                      <tr class="bg-muted font-bold">
                      <td class="text-center"></td>
                      <td class="text-center">合計</td>
                      <td class="text-right">${totalOverdueAmount.toLocaleString()}</td>
                      <td class="text-right">${totalOverdueMonths.toLocaleString()}</td>
                      </tr>
                      </tfoot>
                      </table>
                      <div class="pt-2 text-center text-2xl font-bold">僑星福華社區管理委員會 敬啟</div>
                      <div class="text-center text-2xl font-bold">
                      中華民國 ${new Date().getFullYear() - 1911} 年 ${new Date().getMonth() + 1} 月 ${new Date().getDate()} 日
                      </div>
                      </body>
                    </html>
                    `);
                  printWindow.document.close();
                  printWindow.print();
                }
              }}
            >
              列印
            </Button>
          </div>
        </div>
        <div className="overflow-x-auto max-h-[60vh]">
          <div className="py-2 text-center text-lg font-bold tracking-widest">
            【管理費逾期未繳納名單】
          </div>
          <table className="min-w-full border rounded-lg shadow-lg">
            <thead className="text-sm">
              <tr className="bg-muted">
                <th className="w-10 px-1 py-1 border">序號</th>
                <th className="px-2 py-1 border">住戶編號</th>
                <th className="px-2 py-1 border">累計未繳金額</th>
                <th className="px-2 py-1 border">期數</th>
              </tr>
            </thead>
            <tbody className="text-sm">
              {overdueList.map((item, idx) => (
                <tr key={idx} className="even:bg-gray-50 dark:even:bg-gray-800">
                  <td className="px-1 py-1 border text-center font-bold">{idx + 1}</td>
                  <td className="px-4 py-1 border text-center font-bold">{item.id}</td>
                  <td className="px-4 py-1 border text-right font-bold">
                    {item.amount.toLocaleString()}
                  </td>
                  <td className="px-4 py-1 border text-right font-bold">
                    {parseFloat(item.months)}
                  </td>
                </tr>
              ))}
            </tbody>
            <tfoot>
              <tr className="bg-muted font-bold text-sm">
                <td className="text-center"></td>
                <td className="px-4 py-1 border text-center">合計</td>
                <td className="px-4 py-1 border text-right">
                  {totalOverdueAmount.toLocaleString()}
                </td>
                <td className="px-4 py-1 border text-right">
                  {totalOverdueMonths}
                </td>
              </tr>
            </tfoot>
          </table>
          <div className="pt-4 text-center text-sm font-bold">
            僑星福華社區管理委員會 敬啟
          </div>
          <div className="text-center text-sm font-bold">
            中華民國 {new Date().getFullYear() - 1911} 年{" "}
            {new Date().getMonth() + 1} 月 {new Date().getDate()} 日
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
