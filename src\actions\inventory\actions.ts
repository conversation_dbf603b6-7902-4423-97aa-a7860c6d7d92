"use server";

import { ItemType, Role } from "@/lib/types";
import { SaleFormData, InventoryData } from "@/types/inventory";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";

function convertDecimalValue(v: any) {
  if (v && typeof v === "object" && typeof v.toNumber === "function") {
    try {
      return (v as any).toNumber();
    } catch (e) {
      return Number(String(v));
    }
  }
  return v;
}

function sanitizeInventoryRecord(item: any) {
  return {
    ...item,
    price: convertDecimalValue(item.price),
  };
}

function sanitizeSaleRecord(sale: any) {
  return {
    ...sale,
    totalAmount: convertDecimalValue(sale.totalAmount),
    saleItems: (sale.saleItems || []).map((si: any) => ({
      ...si,
      unitPrice: convertDecimalValue(si.unitPrice),
      lineTotal: convertDecimalValue(si.lineTotal),
      inventory: si.inventory ? sanitizeInventoryRecord(si.inventory) : si.inventory,
    })),
    resident: sale.resident
      ? {
          ...sale.resident,
          squareFootage: convertDecimalValue((sale.resident as any).squareFootage),
        }
      : sale.resident,
  };
}

async function checkWorkspaceMembership(userId: string, workspaceId: string) {
  const member = await prisma.member.findFirst({
    where: {
      userId,
      workspaceId,
    },
  });
  return member;
}

export async function isSuperUser(workspaceId: string) {
  const { userId } = await auth();
  if (!userId) return false;
  const member = await checkWorkspaceMembership(userId, workspaceId);
  return member?.role === Role.ADMIN;
}

export async function getInventory(
  workspaceId: string
): Promise<InventoryData[]> {
  const { userId } = await auth();
  if (!userId) throw new Error("Not authenticated");
  const member = await checkWorkspaceMembership(userId, workspaceId);
  if (!member) throw new Error("Not a member of this workspace");

  const inventory = await prisma.inventory.findMany({
    where: { workspaceId },
    select: {
      itemType: true,
      current: true,
      price: true,
    },
    orderBy: {
      itemType: "asc",
    },
  });
  console.log("inventory: ", inventory);
  return inventory.map((i) => sanitizeInventoryRecord(i));
}

export async function getInventoryByType(
  workspaceId: string,
  itemType: ItemType
) {
  const { userId } = await auth();
  if (!userId) throw new Error("Not authenticated");
  const member = await checkWorkspaceMembership(userId, workspaceId);
  if (!member) throw new Error("Not a member of this workspace");

  const rec = await prisma.inventory.findUnique({
    where: { itemType_workspaceId: { itemType, workspaceId } },
  });
  if (!rec) return rec;
  return sanitizeInventoryRecord(rec);
}

export async function processSale(workspaceId: string, saleData: SaleFormData) {
  const { userId } = await auth();
  if (!userId) throw new Error("Not authenticated");
  const member = await checkWorkspaceMembership(userId, workspaceId);
  if (!member) throw new Error("Not a member of this workspace");

  const {
    itemType,
    quantity,
    unitPrice,
    purchaserName,
    address,
    receiptNo,
    recipient,
    date,
    purchaserType: explicitPurchaserType,
  } = saleData;

  return await prisma.$transaction(async (tx) => {
    const inventory = await tx.inventory.findUnique({
      where: { itemType_workspaceId: { itemType, workspaceId } },
    });

    if (!inventory || inventory.current < quantity) {
      throw new Error(`Insufficient stock for ${itemType}`);
    }

    // Resolve the unit (owner) by resident.residentId if address contains the unit code.
    const unitCode = String(address || "").trim();
    let owner = null;
    if (unitCode) {
      owner = await tx.resident
        .findUnique({
          where: {
            residentId_workspaceId: { residentId: unitCode, workspaceId },
          },
        })
        .catch(() => null);
    }

    // Determine purchaser: could be owner or a tenant (or an ad-hoc purchaser)
    let purchaser: any = null;
    let purchaserType: "OWNER" | "TENANT" | undefined = undefined;

    if (owner) {
      // If explicit purchaser type is provided, respect it
      if (explicitPurchaserType === "OWNER") {
        purchaser = owner;
        purchaserType = "OWNER";
      } else if (explicitPurchaserType === "TENANT") {
        // find or create tenant under owner with purchaserName
        purchaser = await tx.resident.findFirst({
          where: {
            ownerResidentId: owner.id,
            name: purchaserName,
            workspaceId,
            type: "TENANT",
          },
        });
        if (!purchaser) {
          purchaser = await tx.resident.create({
            data: {
              name: purchaserName || "",
              address: unitCode || owner.address || "",
              ownerResidentId: owner.id,
              type: "TENANT",
              workspaceId,
            },
          });
        }
        purchaserType = "TENANT";
      } else {
        // auto-detect
        if (
          !purchaserName ||
          purchaserName.trim() === "" ||
          purchaserName.trim() === owner.name
        ) {
          purchaser = owner;
          purchaserType = "OWNER";
        } else {
          // Try to find a tenant under the owner with this name
          purchaser = await tx.resident.findFirst({
            where: {
              ownerResidentId: owner.id,
              name: purchaserName,
              workspaceId,
              type: "TENANT",
            },
          });
          if (purchaser) {
            purchaserType = "TENANT";
          } else {
            // Create a tenant record tied to owner
            purchaser = await tx.resident.create({
              data: {
                name: purchaserName || "",
                address: unitCode || owner.address || "",
                ownerResidentId: owner.id,
                type: "TENANT",
                workspaceId,
              },
            });
            purchaserType = "TENANT";
          }
        }
      }
    } else {
      // No owner found: fall back to find-or-create purchaser (unit-less)
      purchaser = await tx.resident.findFirst({
        where: { name: purchaserName, address: address, workspaceId },
      });
      if (!purchaser) {
        purchaser = await tx.resident.create({
          data: {
            name: purchaserName || "",
            address: address || "",
            workspaceId,
          },
        });
      }
      purchaserType = undefined;
    }

    // Build sale payload using owner as residentId when available (unit), otherwise purchaser.id
    const salePayload: any = {
      date: new Date(date),
      receiptNo: receiptNo || undefined,
      recipient: recipient || undefined,
      residentId: owner ? owner.id : purchaser.id,
      purchaserId: purchaser.id,
      purchaserType: purchaserType,
      purchaserName: purchaser.name || purchaserName || "",
      address: unitCode || address || "",
      totalAmount: quantity * unitPrice,
      workspaceId,
      saleItems: {
        create: [
          {
            inventoryId: inventory.id,
            itemType: itemType,
            quantity: quantity,
            unitPrice: unitPrice,
            lineTotal: quantity * unitPrice,
          },
        ],
      },
    };

    const sale = await tx.sale.create({ data: salePayload });

    const newStock = inventory.current - quantity;
    await tx.inventory.update({
      where: { id: inventory.id },
      data: { current: newStock },
    });

    await tx.stockMovement.create({
      data: {
        inventoryId: inventory.id,
        type: "SALE",
        quantity: -quantity,
        reason: `Sale to ${purchaserName}`,
        reference: sale.id,
        previousStock: inventory.current,
        newStock: newStock,
        workspaceId,
        memberId: member.id,
      },
    });

    return sale;
  });
}

export async function updateInventoryStock(
  workspaceId: string,
  itemType: ItemType,
  quantityChange: number,
  reason: string = "Manual adjustment"
) {
  const { userId } = await auth();
  if (!userId) throw new Error("Not authenticated");
  const member = await checkWorkspaceMembership(userId, workspaceId);
  if (!member) throw new Error("Not a member of this workspace");

  return await prisma.$transaction(async (tx) => {
    const inventory = await tx.inventory.findUnique({
      where: { itemType_workspaceId: { itemType, workspaceId } },
    });

    if (!inventory) {
      throw new Error(`Inventory not found for ${itemType}`);
    }

    const newStock = Math.max(0, inventory.current + quantityChange);

    await tx.inventory.update({
      where: { id: inventory.id },
      data: { current: newStock },
    });

    await tx.stockMovement.create({
      data: {
        inventoryId: inventory.id,
        type: quantityChange > 0 ? "PURCHASE" : "ADJUSTMENT",
        quantity: quantityChange,
        reason: reason,
        previousStock: inventory.current,
        newStock: newStock,
        workspaceId,
        memberId: member.id,
      },
    });

    return { previousStock: inventory.current, newStock };
  });
}

export async function getSalesHistory(
  workspaceId: string,
  limit: number = 100
) {
  const { userId } = await auth();
  if (!userId) throw new Error("Not authenticated");
  const member = await checkWorkspaceMembership(userId, workspaceId);
  if (!member) throw new Error("Not a member of this workspace");

  const sales = await prisma.sale.findMany({
    where: { workspaceId },
    include: {
      saleItems: {
        include: {
          inventory: true,
        },
      },
      resident: true,
    },
    orderBy: {
      createdAt: "desc",
    },
    take: limit,
  });

  return sales.map((s) => sanitizeSaleRecord(s));
}

export async function deleteSale(workspaceId: string, saleId: string) {
  const { userId } = await auth();
  if (!userId) throw new Error("Not authenticated");
  const member = await checkWorkspaceMembership(userId, workspaceId);
  if (!member || member.role !== Role.ADMIN) {
    throw new Error("Unauthorized action.");
  }

  return await prisma.$transaction(async (tx) => {
    const sale = await tx.sale.findUnique({
      where: { id: saleId, workspaceId },
      include: { saleItems: true },
    });

    if (!sale) {
      throw new Error("Sale not found");
    }

    for (const item of sale.saleItems) {
      const inventory = await tx.inventory.findUnique({
        where: { id: item.inventoryId },
      });

      if (inventory) {
        const newStock = inventory.current + item.quantity;
        await tx.inventory.update({
          where: { id: item.inventoryId },
          data: { current: newStock },
        });

        await tx.stockMovement.create({
          data: {
            inventoryId: item.inventoryId,
            type: "ADJUSTMENT",
            quantity: item.quantity,
            reason: `Return from deleted sale ${sale.id}`,
            reference: sale.id,
            previousStock: inventory.current,
            newStock: newStock,
            workspaceId,
            memberId: member.id,
          },
        });
      }
    }

    await tx.saleItem.deleteMany({ where: { saleId: sale.id } });
    const deletedSale = await tx.sale.delete({ where: { id: saleId } });

    return deletedSale;
  });
}

export async function updateSale(
  workspaceId: string,
  saleId: string,
  newData: SaleFormData
) {
  const { userId } = await auth();
  if (!userId) throw new Error("Not authenticated");
  const member = await checkWorkspaceMembership(userId, workspaceId);
  if (!member || member.role !== Role.ADMIN) {
    throw new Error("Unauthorized action.");
  }

  return await prisma.$transaction(async (tx) => {
    const sale = await tx.sale.findUnique({
      where: { id: saleId, workspaceId },
      include: { saleItems: true },
    });

    if (!sale) throw new Error("Sale not found");
    const originalItem = sale.saleItems[0];
    if (!originalItem) throw new Error("Sale item not found");

    const newInventoryInfo = await tx.inventory.findUnique({
      where: {
        itemType_workspaceId: { itemType: newData.itemType, workspaceId },
      },
    });
    if (!newInventoryInfo)
      throw new Error("New inventory item type not found.");

    if (originalItem.itemType === newData.itemType) {
      const quantityDiff = newData.quantity - originalItem.quantity;
      if (quantityDiff !== 0) {
        const inventory = await tx.inventory.findUnique({
          where: { id: newInventoryInfo.id },
        });
        if (!inventory) throw new Error("Inventory not found");
        const newStock = inventory.current - quantityDiff;
        if (newStock < 0) throw new Error("Insufficient stock");
        await tx.inventory.update({
          where: { id: inventory.id },
          data: { current: newStock },
        });
      }
    } else {
      const originalInventory = await tx.inventory.findUnique({
        where: {
          itemType_workspaceId: {
            itemType: originalItem.itemType,
            workspaceId,
          },
        },
      });
      if (!originalInventory) throw new Error("Original inventory not found");
      await tx.inventory.update({
        where: { id: originalInventory.id },
        data: { current: originalInventory.current + originalItem.quantity },
      });

      if (newInventoryInfo.current < newData.quantity)
        throw new Error("Insufficient stock for new item");
      await tx.inventory.update({
        where: { id: newInventoryInfo.id },
        data: { current: newInventoryInfo.current - newData.quantity },
      });
    }

    let updatedReceiptNo: string | null | undefined = newData.receiptNo;
    if (updatedReceiptNo === "") {
      updatedReceiptNo = null;
    }

    if (
      updatedReceiptNo !== sale.receiptNo &&
      updatedReceiptNo !== null &&
      updatedReceiptNo !== undefined
    ) {
      const existingSaleWithReceiptNo = await tx.sale.findFirst({
        where: {
          receiptNo: updatedReceiptNo,
          workspaceId,
          id: { not: saleId },
        },
      });

      if (existingSaleWithReceiptNo) {
        throw new Error(
          `Receipt number '${updatedReceiptNo}' is already in use by another sale.`
        );
      }
    }

    await tx.sale.update({
      where: { id: saleId },
      data: {
        date: new Date(newData.date),
        receiptNo: updatedReceiptNo,
        recipient: newData.recipient,
        purchaserName: newData.purchaserName,
        purchaserType: newData.purchaserType || undefined,
        address: newData.address,
        totalAmount: newData.quantity * newData.unitPrice,
      },
    });

    await tx.saleItem.update({
      where: { id: originalItem.id },
      data: {
        inventoryId: newInventoryInfo.id,
        itemType: newData.itemType,
        quantity: newData.quantity,
        unitPrice: newData.unitPrice,
        lineTotal: newData.quantity * newData.unitPrice,
      },
    });

    return sale;
  });
}
