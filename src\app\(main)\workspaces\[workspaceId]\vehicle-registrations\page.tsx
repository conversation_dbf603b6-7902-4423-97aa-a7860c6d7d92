"use client";

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useState, use } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

type VR = {
  id: string;
  licensePlate: string;
  vehicleType: string;
  brand?: string | null;
  model?: string | null;
  color?: string | null;
  parkingSpaceId: string;
  residentId: string;
  parkingFeePayer: 'OWNER' | 'RENTER' | 'CUSTOM';
  isActive?: boolean;
};

function VRForm({ initial, onClose, workspaceId, onSaved }: { initial?: Partial<VR> | null; onClose: () => void; workspaceId: string; onSaved: () => void; }) {
  const { register, handleSubmit, setValue, watch } = useForm<VR>({
    defaultValues: {
      licensePlate: initial?.licensePlate || '',
      vehicleType: initial?.vehicleType || 'CAR',
      brand: initial?.brand || '',
      model: initial?.model || '',
      color: initial?.color || '',
      parkingSpaceId: initial?.parkingSpaceId || '',
      residentId: initial?.residentId || '',
      parkingFeePayer: initial?.parkingFeePayer || 'OWNER',
      isActive: initial?.isActive ?? true,
    },
  });

  const queryClient = useQueryClient();

  const createMut = useMutation({
    mutationFn: async (payload: any) => {
      const res = await fetch(`/api/workspaces/${workspaceId}/vehicle-registrations`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
      if (!res.ok) throw new Error('Failed to create vehicle registration');
      return res.json();
    },
    onSuccess: () => { toast.success('Created'); queryClient.invalidateQueries({ queryKey: ['vehicle-registrations', workspaceId] }); onSaved(); },
    onError: (err: any) => toast.error(err.message || 'Failed'),
  });

  const updateMut = useMutation({
    mutationFn: async ({ id, payload }: { id: string; payload: any }) => {
      const res = await fetch(`/api/workspaces/${workspaceId}/vehicle-registrations/${id}`, { method: 'PUT', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
      if (!res.ok) throw new Error('Failed to update');
      return res.json();
    },
    onSuccess: () => { toast.success('Updated'); queryClient.invalidateQueries({ queryKey: ['vehicle-registrations', workspaceId] }); onSaved(); },
    onError: (err: any) => toast.error(err.message || 'Failed'),
  });

  const onSubmit = handleSubmit((vals) => {
    const payload = { ...vals };
    if (initial && initial.id) {
      updateMut.mutate({ id: initial.id!, payload });
    } else {
      createMut.mutate(payload);
    }
  });

  return (
    <form onSubmit={onSubmit} className="space-y-3">
      <div>
        <Label>License Plate</Label>
        <Input {...register('licensePlate')} />
      </div>
      <div>
        <Label>Vehicle Type</Label>
        <Input {...register('vehicleType')} />
      </div>
      <div>
        <Label>Brand</Label>
        <Input {...register('brand')} />
      </div>
      <div>
        <Label>Model</Label>
        <Input {...register('model')} />
      </div>
      <div>
        <Label>Color</Label>
        <Input {...register('color')} />
      </div>
      <div>
        <Label>Parking Space ID</Label>
        <Input {...register('parkingSpaceId')} />
      </div>
      <div>
        <Label>Resident ID</Label>
        <Input {...register('residentId')} />
      </div>
      <div>
        <Label>Annual Fee Payer</Label>
        <Select
          value={watch('parkingFeePayer')}
          onValueChange={(value) => setValue('parkingFeePayer', value as VR['parkingFeePayer'])}
        >
          <SelectTrigger><SelectValue /></SelectTrigger>
          <SelectContent>
            <SelectItem value="OWNER">Owner</SelectItem>
            <SelectItem value="RENTER">Renter</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={onClose}>Cancel</Button>
        <Button type="submit">Save</Button>
      </div>
    </form>
  );
}

export default function VehicleRegistrationsPage(props: { params: Promise<{ workspaceId: string }> }) {
  const params = use(props.params);
  const workspaceId = params.workspaceId;
  const [open, setOpen] = useState(false);
  const [editing, setEditing] = useState<VR | null>(null);

  const { data, isLoading } = useQuery({
    queryKey: ['vehicle-registrations', workspaceId],
    queryFn: async () => {
      const res = await fetch(`/api/workspaces/${workspaceId}/vehicle-registrations`);
      if (!res.ok) throw new Error('Failed');
      const json = await res.json();
      if (!json.success) throw new Error(json.error || 'Failed');
      return json.data as VR[];
    },
  });

  const queryClient = useQueryClient();
  const deleteMut = useMutation({
    mutationFn: async (id: string) => {
      const res = await fetch(`/api/workspaces/${workspaceId}/vehicle-registrations/${id}`, { method: 'DELETE' });
      if (!res.ok) throw new Error('Failed to delete');
      return res.json();
    },
    onSuccess: () => { toast.success('Deleted'); queryClient.invalidateQueries({ queryKey: ['vehicle-registrations', workspaceId] }); },
    onError: (err: any) => toast.error(err.message || 'Failed'),
  });

  const handleNew = () => { setEditing(null); setOpen(true); };
  const handleEdit = (item: VR) => { setEditing(item); setOpen(true); };
  const handleDelete = (id: string) => { if (confirm('Delete?')) deleteMut.mutate(id); };

  return (
    <Card className="w-full max-w-5xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Vehicle Registrations</CardTitle>
          <Button onClick={handleNew}>New</Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading && <div>Loading...</div>}
        {!isLoading && data && data.length === 0 && <div>No registrations</div>}
        {!isLoading && data && data.length > 0 && (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Plate</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Brand</TableHead>
                <TableHead>Model</TableHead>
                <TableHead>Resident</TableHead>
                <TableHead>Fee Payer</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.map((r) => (
                <TableRow key={r.id}>
                  <TableCell>{r.licensePlate}</TableCell>
                  <TableCell>{r.vehicleType}</TableCell>
                  <TableCell>{r.brand || '-'}</TableCell>
                  <TableCell>{r.model || '-'}</TableCell>
                  <TableCell>{r.residentId}</TableCell>
                  <TableCell className="capitalize">{r.parkingFeePayer.toLowerCase()}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button variant="ghost" size="sm" onClick={() => handleEdit(r)}>Edit</Button>
                      <Button variant="destructive" size="sm" onClick={() => handleDelete(r.id)}>Delete</Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{editing ? 'Edit Registration' : 'New Registration'}</DialogTitle>
          </DialogHeader>
          <VRForm initial={editing} onClose={() => setOpen(false)} workspaceId={workspaceId} onSaved={() => setOpen(false)} />
        </DialogContent>
      </Dialog>
    </Card>
  );
}
