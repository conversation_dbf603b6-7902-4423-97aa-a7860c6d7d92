import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { Decimal } from '@prisma/client/runtime/library';
import { auth } from '@clerk/nextjs/server';

type PreviewRow = {
  rowNumber: number;
  raw: string[];
  mapped: Record<string, any>;
  errors: string[];
};

function parseCSV(text: string): string[][] {
  const rows: string[][] = [];
  let cur = '';
  let row: string[] = [];
  let inQuotes = false;
  for (let i = 0; i < text.length; i++) {
    const ch = text[i];
    const next = text[i + 1];
    if (ch === '"') {
      if (inQuotes && next === '"') {
        cur += '"';
        i++;
      } else {
        inQuotes = !inQuotes;
      }
      continue;
    }
    if (!inQuotes && ch === ',') {
      row.push(cur);
      cur = '';
      continue;
    }
    if (!inQuotes && (ch === '\n' || ch === '\r')) {
      if (ch === '\r' && next === '\n') continue;
      row.push(cur);
      rows.push(row);
      row = [];
      cur = '';
      continue;
    }
    cur += ch;
  }
  if (cur !== '' || row.length > 0) row.push(cur);
  if (row.length > 0) rows.push(row);
  return rows.map(r => r.map(c => c.trim()));
}

function normalizeAmount(v: string): number {
  if (!v) return 0;
  const cleaned = String(v).replace(/[^0-9.\-]/g, '');
  const n = parseFloat(cleaned || '0');
  if (isNaN(n)) return 0;
  return Math.round(n * 100);
}

export async function POST(req: NextRequest, props: { params: Promise<{ workspaceId: string }> }) {
  try {
    const workspaceId = (await props.params).workspaceId;
    const form = await req.formData();
    const file = form.get('file') as File | null;
    const dryRun = String(form.get('dryRun') || 'true') === 'true';

    if (!file) return NextResponse.json({ success: false, message: 'No file provided' }, { status: 400 });
    const name = file.name || '';
    const text = await file.text();

    const rows = parseCSV(text);
    if (!rows || rows.length === 0) return NextResponse.json({ success: false, message: 'No rows parsed' }, { status: 400 });

    const header = rows[0].map(h => h.replace(/^"|"$/g, '').trim());

    const idxOf = (candidates: string[]) => {
      const lc = header.map(h => h.toLowerCase());
      for (const cand of candidates) {
        const i = lc.findIndex(h => h.includes(cand));
        if (i !== -1) return i;
      }
      return -1;
    };

    const idxSequence = idxOf(['序號', 'no', 'id', 'serial']);
    const idxHubei = idxOf(['戶別', 'hubei', 'unit']);
    // Do NOT use 住戶編號 for mapping; map 戶別 to residentId instead
    const idxName = idxOf(['姓名', 'name']);
    const idxSquare = idxOf(['坪數', 'square']);
    const idxPaymentMethod = idxOf(['繳款方式', 'payment']);
    const idxCombined = idxOf(['合併', 'combined']);
    const idxSpace = idxOf(['車位', 'space', '車位號碼']);
    const idxManagementFee = idxOf(['管理費', 'management', 'base_fee']);
    const idxTotal = idxOf(['應繳金額', 'total', 'total_amount']);

    const preview: PreviewRow[] = [];
    let rowsProcessed = 0;
    let rowsFailed = 0;

    for (let r = 1; r < rows.length; r++) {
      const raw = rows[r];
      const mapped: Record<string, any> = {};
      const rowErrors: string[] = [];
      const seq = idxSequence !== -1 ? raw[idxSequence] || '' : String(r);
      const hubei = idxHubei !== -1 ? raw[idxHubei] || '' : '';
      // Map residentId from 戶別 (`hubei`) only
      const residentId = hubei || '';
      const nameVal = idxName !== -1 ? raw[idxName] || '' : '';
      const square = idxSquare !== -1 ? raw[idxSquare] || '' : '';
      const paymentMethod = idxPaymentMethod !== -1 ? raw[idxPaymentMethod] || '' : '';
      const combined = idxCombined !== -1 ? raw[idxCombined] || '' : '';
      const space = idxSpace !== -1 ? raw[idxSpace] || '' : '';
      const managementFee = idxManagementFee !== -1 ? raw[idxManagementFee] || raw[idxTotal] || '' : raw[idxTotal] || '';

      mapped.sequenceNumber = seq;
      mapped.hubei = hubei;
      mapped.residentId = residentId;
      mapped.name = nameVal;
      mapped.squareFootage = square;
      mapped.paymentMethod = paymentMethod;
      mapped.isCombined = String(combined).toLowerCase().startsWith('y') || String(combined).includes('合併');
      mapped.spaceNumber = space;
      mapped.managementFee = normalizeAmount(managementFee);

      if (!mapped.residentId || String(mapped.residentId).trim() === '') {
        rowErrors.push('Missing resident identifier (戶別)');
      }
      if (mapped.managementFee <= 0) {
        rowErrors.push('管理費 or 應繳金額 parsed as 0');
      }

      if (rowErrors.length) rowsFailed++;
      rowsProcessed++;

      preview.push({ rowNumber: r + 1, raw, mapped, errors: rowErrors });
    }

    const sample = preview.slice(0, 200);

    // detect missing residents in DB
    let missingResidents: string[] = [];
    if (workspaceId) {
      const ids = Array.from(new Set(preview.map(p => String(p.mapped.residentId).trim()).filter(Boolean)));
      const existing = await prisma.resident.findMany({ where: { residentId: { in: ids }, workspaceId }, select: { residentId: true } });
      const existingSet = new Set(existing.map(e => e.residentId));
      missingResidents = ids.filter(id => !existingSet.has(id));
    }

    const billingPeriod = String(form.get('billingPeriod') || '').trim();

    const applied: { createdResidents: number; updatedResidents: number; createdFees: number; updatedFees: number; errors: Array<{ row: number; error: string }> } = {
      createdResidents: 0,
      updatedResidents: 0,
      createdFees: 0,
      updatedFees: 0,
      errors: [],
    };

    let writerMemberId: string | undefined = undefined;
    if (!dryRun) {
      const { userId: clerkUserId } = await auth();
      if (!clerkUserId) return NextResponse.json({ success: false, message: 'Unauthorized' }, { status: 401 });

      const adminMember = await prisma.member.findFirst({ where: { userId: clerkUserId, workspaceId } });
      if (!adminMember || adminMember.role !== 'ADMIN') {
        return NextResponse.json({ success: false, message: 'Forbidden: must be workspace admin to commit' }, { status: 403 });
      }
      writerMemberId = adminMember.id;

      const bp = billingPeriod || (new Date()).toISOString().slice(0,7);
      const workspaceIdVal = workspaceId || '';

      for (const p of preview) {
        try {
          const rid = String(p.mapped.residentId || '').trim();
          if (!rid) {
            applied.errors.push({ row: p.rowNumber, error: 'Missing residentId' });
            continue;
          }

          let residentRecord: any = null;
          try {
            residentRecord = await prisma.resident.upsert({
              where: { residentId_workspaceId: { residentId: rid, workspaceId: workspaceIdVal } },
              update: {
                name: p.mapped.name || undefined,
                squareFootage: p.mapped.squareFootage ? new Decimal(String(p.mapped.squareFootage).replace(/[^0-9.\-]/g, '')) : undefined,
                paymentMethod: p.mapped.paymentMethod || undefined,
                isCombined: typeof p.mapped.isCombined === 'boolean' ? p.mapped.isCombined : undefined,
              },
              create: {
                residentId: rid,
                name: p.mapped.name || rid,
                address: '',
                squareFootage: p.mapped.squareFootage ? new Decimal(String(p.mapped.squareFootage).replace(/[^0-9.\-]/g, '')) : undefined,
                paymentMethod: p.mapped.paymentMethod || undefined,
                isCombined: typeof p.mapped.isCombined === 'boolean' ? p.mapped.isCombined : false,
                workspaceId: workspaceId || '',
              },
            });
          } catch (e) {
            const msg = e && (e as any).message ? (e as any).message : String(e);
            applied.errors.push({ row: p.rowNumber, error: 'Resident upsert failed: ' + msg });
            continue;
          }

          const amount = typeof p.mapped.managementFee === 'number' ? p.mapped.managementFee : 0;
          if (amount > 0) {
            try {
              await prisma.managementFee.upsert({
                where: { residentId_billingPeriod_feeType: { residentId: residentRecord.id, billingPeriod: bp, feeType: 'MANAGEMENT' } },
                update: {
                  baseFee: amount,
                  totalAmount: amount,
                  updatedAt: new Date(),
                },
                create: {
                  residentId: residentRecord.id,
                  feeType: 'MANAGEMENT',
                  baseFee: amount,
                  parkingFee: 0,
                  vehicleFee: 0,
                  totalAmount: amount,
                  squareFootage: residentRecord.squareFootage || undefined,
                  billingPeriod: bp,
                  dueDate: new Date(),
                  workspaceId: workspaceId || '',
                },
              });
            } catch (e) {
              const msg = e && (e as any).message ? (e as any).message : String(e);
              applied.errors.push({ row: p.rowNumber, error: 'ManagementFee upsert failed: ' + msg });
            }
          }

        } catch (err) {
          const msg = err && (err as any).message ? (err as any).message : String(err);
          applied.errors.push({ row: p.rowNumber, error: msg });
        }
      }

      try {
        if (writerMemberId) {
          await prisma.residentImport.create({
            data: {
              fileName: name,
              fileType: 'CSV',
              rowsProcessed,
              rowsSuccess: rowsProcessed - rowsFailed,
              rowsFailed,
              workspaceId: workspaceId || '',
              memberId: writerMemberId,
              errors: applied.errors.length ? applied.errors : undefined,
            },
          });
        }
      } catch (e) {
        console.warn('Failed to create ResidentImport record', e);
      }
    }

    const result = {
      success: true,
      fileName: name,
      rows: preview.length,
      rowsProcessed,
      rowsFailed,
      missingResidentsCount: missingResidents.length,
      missingResidents: missingResidents.slice(0, 50),
      preview: sample,
      dryRun: !!dryRun,
      applied: dryRun ? undefined : applied,
    };

    return NextResponse.json(result);
  } catch (err: any) {
    console.error('Import parse error', err);
    return NextResponse.json({ success: false, message: String(err?.message || err) }, { status: 500 });
  }
}
