import { NextResponse } from 'next/server';
import { getFeeConfigurations, createFeeConfiguration } from '@/actions/fees/actions';

export async function GET(req: Request, { params }: { params: { workspaceId: string } }) {
  const { workspaceId } = params;
  const res = await getFeeConfigurations(workspaceId);
  return NextResponse.json(res);
}

export async function POST(req: Request, props: { params: Promise<{ workspaceId: string }> }) {
  const { workspaceId } = await props.params;
  const body = await req.json();
  const payload = {
    feeType: body.feeType,
    unitPrice: body.unitPrice,
    description: body.description,
    effectiveFrom: body.effectiveFrom,
    effectiveTo: body.effectiveTo,
    isActive: body.isActive,
  };
  const res = await createFeeConfiguration(workspaceId, payload);
  return NextResponse.json(res);
}
