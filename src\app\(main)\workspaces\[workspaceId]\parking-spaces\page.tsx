// src/app/(main)/workspaces/[workspaceId]/parking-spaces/page.tsx
"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useState, use, Suspense } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import ResidentSearch from "@/components/resident-search"; // Import ResidentSearch

// The ResidentOption type is no longer needed as ResidentSearch handles it internally

type ParkingSpace = {
  id: string;
  spaceNumber: string;
  spaceType: "CAR" | "MOTORCYCLE" | "BICYCLE";
  residentDbId: string; // The actual DB ID of the resident
  residentUnitId: string; // The unit ID (e.g., "06-01-1") used for display and search
  resident?: { name: string; residentId: string }; // Include resident info for display
  isActive?: boolean;
  notes?: string | null;
};

// Form component for creating/editing Parking Spaces
function ParkingSpaceForm({
  initial,
  onClose,
  workspaceId,
  onSaved,
}: {
  initial?: Partial<ParkingSpace> | null;
  onClose: () => void;
  workspaceId: string;
  onSaved: () => void;
}) {
  const { register, handleSubmit, setValue, watch } = useForm<ParkingSpace>({
    defaultValues: {
      spaceNumber: initial?.spaceNumber || "",
      spaceType: initial?.spaceType || "CAR",
      residentUnitId: initial?.resident?.residentId || "", // Use residentUnitId for the search component
      isActive: initial?.isActive ?? true,
      notes: initial?.notes || "",
    },
  });

  const handleFormChange = (field: keyof ParkingSpace, value: any) => {
    setValue(field, value);
  };

  const queryClient = useQueryClient();

  const createMut = useMutation({
    mutationFn: async (payload: any) => {
      const res = await fetch(`/api/workspaces/${workspaceId}/parking-spaces`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
      if (!res.ok) throw new Error("Failed to create parking space");
      return res.json();
    },
    onSuccess: () => {
      toast.success("Created");
      queryClient.invalidateQueries({
        queryKey: ["parking-spaces", workspaceId],
      });
      onSaved();
    },
    onError: (err: any) => toast.error(err.message || "Failed"),
  });

  const updateMut = useMutation({
    mutationFn: async ({ id, payload }: { id: string; payload: any }) => {
      const res = await fetch(
        `/api/workspaces/${workspaceId}/parking-spaces/${id}`,
        {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payload),
        }
      );
      if (!res.ok) throw new Error("Failed to update");
      return res.json();
    },
    onSuccess: () => {
      toast.success("Updated");
      queryClient.invalidateQueries({
        queryKey: ["parking-spaces", workspaceId],
      });
      onSaved();
    },
    onError: (err: any) => toast.error(err.message || "Failed"),
  });

  const onSubmit = handleSubmit((vals) => {
    // Pass residentUnitId to the action, which will then resolve it to residentDbId
    const payload = { ...vals, residentUnitId: vals.residentUnitId };
    if (initial && initial.id) {
      updateMut.mutate({ id: initial.id!, payload });
    } else {
      createMut.mutate(payload);
    }
  });

  return (
    <form onSubmit={onSubmit} className="space-y-3">
      <div>
        <Label htmlFor="spaceNumber">Space Number</Label>
        <Input id="spaceNumber" {...register("spaceNumber")} required />
      </div>
      <div>
        <Label htmlFor="spaceType">Space Type</Label>
        <Select
          value={watch("spaceType")}
          onValueChange={(value) =>
            setValue("spaceType", value as ParkingSpace["spaceType"])
          }
        >
          <SelectTrigger id="spaceType">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="CAR">Car</SelectItem>
            <SelectItem value="MOTORCYCLE">Motorcycle</SelectItem>
            <SelectItem value="BICYCLE">Bicycle</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div>
        <Label htmlFor="residentUnitId">Owner (Resident Unit ID)</Label>

        {/* Replace free-text address with searchable resident selector */}
        <Suspense
          fallback={
            <input
              type="text"
              value={}
              onChange={(e) =>
                handleFormChange("residentUnitId", e.target.value)
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="例: 06-01-1"
              required
            />
          }
        >
          <ResidentSearch
            workspaceId={workspaceId}
            value={watch("residentUnitId")}
            onChange={(value) => setValue("residentUnitId", value)}
            placeholder="Search by Resident Unit ID or Name"
          />
        </Suspense>
      </div>
      <div>
        <Label htmlFor="notes">Notes</Label>
        <Input id="notes" {...register("notes")} />
      </div>
      {/* isActive checkbox can be added here if needed */}

      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button type="submit">Save</Button>
      </div>
    </form>
  );
}

// Main page component
export default function ParkingSpacesPage(props: {
  params: Promise<{ workspaceId: string }>;
}) {
  const params = use(props.params);
  const workspaceId = params.workspaceId;
  const [open, setOpen] = useState(false);
  const [editing, setEditing] = useState<ParkingSpace | null>(null);

  const { data, isLoading, error } = useQuery<ParkingSpace[]>({
    queryKey: ["parking-spaces", workspaceId],
    queryFn: async () => {
      const res = await fetch(`/api/workspaces/${workspaceId}/parking-spaces`);
      if (!res.ok) throw new Error("Failed to fetch parking spaces");
      const json = await res.json();
      if (!json.success)
        throw new Error(json.error || "Failed to fetch parking spaces");
      return json.data;
    },
    enabled: !!workspaceId,
  });

  const queryClient = useQueryClient();
  const deleteMut = useMutation({
    mutationFn: async (id: string) => {
      const res = await fetch(
        `/api/workspaces/${workspaceId}/parking-spaces/${id}`,
        { method: "DELETE" }
      );
      if (!res.ok) throw new Error("Failed to delete parking space");
      return res.json();
    },
    onSuccess: () => {
      toast.success("Deleted");
      queryClient.invalidateQueries({
        queryKey: ["parking-spaces", workspaceId],
      });
    },
    onError: (err: any) => toast.error(err.message || "Failed"),
  });

  const handleNew = () => {
    setEditing(null);
    setOpen(true);
  };
  const handleEdit = (item: ParkingSpace) => {
    setEditing(item);
    setOpen(true);
  };
  const handleDelete = (id: string) => {
    if (confirm("Are you sure you want to delete this parking space?"))
      deleteMut.mutate(id);
  };

  if (error) return <div className="text-red-500">Error: {error.message}</div>;

  return (
    <Card className="w-full max-w-5xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Parking Spaces</CardTitle>
          <Button onClick={handleNew}>Add Parking Space</Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading && <div>Loading parking spaces...</div>}
        {!isLoading && data && data.length === 0 && (
          <div>No parking spaces found.</div>
        )}
        {!isLoading && data && data.length > 0 && (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Space Number</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Owner</TableHead>
                <TableHead>Notes</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.map((space) => (
                <TableRow key={space.id}>
                  <TableCell>{space.spaceNumber}</TableCell>
                  <TableCell className="capitalize">
                    {space.spaceType.toLowerCase()}
                  </TableCell>
                  <TableCell>
                    {space.resident?.name || "N/A"} (
                    {space.resident?.residentId || "N/A"})
                  </TableCell>
                  <TableCell>{space.notes || "-"}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(space)}
                      >
                        Edit
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDelete(space.id)}
                      >
                        Delete
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {editing ? "Edit Parking Space" : "Add New Parking Space"}
            </DialogTitle>
          </DialogHeader>
          <ParkingSpaceForm
            initial={editing}
            onClose={() => setOpen(false)}
            workspaceId={workspaceId}
            onSaved={() => setOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </Card>
  );
}
