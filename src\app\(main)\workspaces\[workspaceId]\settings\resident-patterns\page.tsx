import { getResidentPatterns } from "@/actions/resident-patterns";
import { ResidentPatternList } from "./resident-pattern-list";

export default async function ResidentPatternsPage(
  props: {
  params: Promise<{ workspaceId: string }>;
}) {
  const params = await props.params;
  const patterns = await getResidentPatterns(params.workspaceId);

  return (
    <div className="w-full max-w-7xl mx-auto space-y-6 p-4 pt-0">
      <div>
        <h3 className="text-lg font-medium">Resident Patterns</h3>
        <p className="text-sm text-muted-foreground">
          Manage the patterns used to identify residents from transaction data.
        </p>
      </div>
      <ResidentPatternList patterns={patterns} workspaceId={params.workspaceId} />
    </div>
  );
}
