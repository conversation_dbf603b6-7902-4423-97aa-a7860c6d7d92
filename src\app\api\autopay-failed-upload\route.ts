import { NextRequest, NextResponse } from 'next/server';
import { processFailedAutopayRecords, FailedAutopayRecord } from '@/lib/google/append-autopay-failed-to-sheet';
import { uploadImageToMaintenanceFeeDrive } from '@/lib/google/upload-image-to-maintenance-fee-drive';
import { analyzeAutoPayFailedImage } from '@/lib/google/analyze-autopay-resident-image';
import { analyzeAutoPayFailedPDF } from '@/lib/google/analyze-autopay-resident-pdf';

export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData();
    const file = formData.get('file') as File | null;
    const processingDate = String(formData.get('processingDate') || '').trim();

    if (!file) {
      return NextResponse.json({ success: false, message: 'No file uploaded' }, { status: 400 });
    }

    // If the uploaded file is an image or PDF, run the same OCR/AI analysis used for autopay statements
    const mime = file.type || '';
    let records: FailedAutopayRecord[] = [];
    let usedProcessingDate = processingDate;

    function normalizeToADDate(input: string): string {
      if (!input) return '';
      const s = input.trim();
      // ROC with slashes e.g. 114/11/17
      let m = s.match(/^(\d{2,3})\/(\d{1,2})\/(\d{1,2})$/);
      if (m) {
        const y = parseInt(m[1], 10) + 1911;
        const mm = m[2].padStart(2, '0');
        const dd = m[3].padStart(2, '0');
        return `${y}/${mm}/${dd}`;
      }
      // ROC with 年月日 e.g. 114年11月17日
      m = s.match(/^(\d{2,4})年(\d{1,2})月(\d{1,2})日?$/);
      if (m) {
        let y = m[1];
        if (y.length <= 3) {
          y = String(parseInt(y, 10) + 1911);
        }
        const mm = m[2].padStart(2, '0');
        const dd = m[3].padStart(2, '0');
        return `${y}/${mm}/${dd}`;
      }
      // AD with slashes YYYY/MM/DD or YYYY/M/D
      m = s.match(/^(\d{4})\/(\d{1,2})(?:\/(\d{1,2}))?$/);
      if (m) {
        const y = m[1];
        const mm = m[2].padStart(2, '0');
        const dd = m[3] ? m[3].padStart(2, '0') : undefined;
        return dd ? `${y}/${mm}/${dd}` : `${y}/${mm}`;
      }
      // AD with dashes YYYY-MM-DD
      m = s.match(/^(\d{4})-(\d{1,2})-(\d{1,2})$/);
      if (m) {
        const y = m[1];
        const mm = m[2].padStart(2, '0');
        const dd = m[3].padStart(2, '0');
        return `${y}/${mm}/${dd}`;
      }
      return s; // fallback: return original
    }

    if (mime.startsWith('image/') || mime === 'application/pdf') {
      const arrayBuffer = await file.arrayBuffer();
      const base64 = Buffer.from(arrayBuffer).toString('base64');

      // upload image/pdf to Drive with requested filename schema
      try {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const isImage = mime.startsWith('image/');
        const isPDF = mime === 'application/pdf';
        const fileExt = isImage ? 'jpg' : (isPDF ? 'pdf' : 'bin');
        const filename = `auto_pay_resident_failed_${timestamp}.${fileExt}`;
        // upload (ignore result aside from logging)
        await uploadImageToMaintenanceFeeDrive(Buffer.from(arrayBuffer), filename).catch((e) => console.warn('Drive upload failed', e));
      } catch (e) {
        console.warn('Failed to upload failed-autopay file to Drive', e);
      }

      // Call the existing analyzers to extract structured data
      let analyzed: any = null;
      if (mime === 'application/pdf') {
        analyzed = await analyzeAutoPayFailedPDF(base64);
      } else {
        analyzed = await analyzeAutoPayFailedImage(base64);
      }
      console.log("analyzed", analyzed)

      // The analyzer returns object matching autoPayResidentSchema: { processingDate, records: [{ residentId, amount }, ... ] }
      if (analyzed && analyzed.records && Array.isArray(analyzed.records)) {
        // prefer processingDate from analyzer if not provided
        if (!usedProcessingDate && analyzed.processingDate) usedProcessingDate = analyzed.processingDate;
        // normalize to AD date format like YYYY/MM/DD or YYYY/MM
        usedProcessingDate = normalizeToADDate(usedProcessingDate || '');

        let idx = 0;
        records = analyzed.records.map((r: any) => {
          idx++;
          return {
            seq: String(idx),
            // prefer explicit account field from analyzer if present
            account: (r.account || r.acct || r['帳號'] || '').toString(),
            // allow analyzer to supply residentId when available
            residentId: (r.residentId || r.userId || r['用戶代號'] || '').toString(),
            debitCredit: (r.debitCredit || r['借貸別'] || r.type || '').toString(),
            amount: typeof r.amount === 'number' ? r.amount : parseFloat(String(r.amount || '').replace(/[^0-9.-]+/g, '')) || 0,
            resultCode: (r.resultCode || r['轉帳結果代碼'] || r.error || 'FAILED').toString(),
          } as FailedAutopayRecord;
        });
      } else {
        return NextResponse.json({ success: false, message: 'Failed to parse image/PDF with analyzer' }, { status: 400 });
      }
    } else {
      // Fallback: accept plain text and try CSV parsing (legacy)
      const text = await file.text();
      const lines = text.split(/\r?\n/).map(l => l.trim()).filter(Boolean);
      if (lines.length < 2) {
        return NextResponse.json({ success: false, message: 'Uploaded file not an image/pdf and CSV content seems empty' }, { status: 400 });
      }
      // simple CSV parse (best-effort)
      const header = lines[0].split(',').map(h => h.replace(/^"|"$/g, '').trim());
      const idxOf = (candidates: string[]) => {
        const lc = header.map(h => h.toLowerCase());
        for (const cand of candidates) {
          const i = lc.findIndex(h => h.includes(cand));
          if (i !== -1) return i;
        }
        return -1;
      };
      const idxSeq = idxOf(['序號','no','id','serial']);
      const idxAccount = idxOf(['帳號','account','accountno','bank account','帳號/卡號']);
      const idxType = idxOf(['借貸','借貸別','type','debit','credit']);
      const idxAmount = idxOf(['交易金額','金額','amount']);
      const idxResultCode = idxOf(['轉帳結果','結果代碼','result','code']);
      const parsed: FailedAutopayRecord[] = [];
      for (let i = 1; i < lines.length; i++) {
        const cols = lines[i].split(',').map(c => c.replace(/^"|"$/g, '').trim());
        const seq = idxSeq !== -1 ? cols[idxSeq] || String(i) : String(i);
        const account = idxAccount !== -1 ? (cols[idxAccount] || '') : '';
        const debitCredit = idxType !== -1 ? (cols[idxType] || '') : '';
        const amount = idxAmount !== -1 ? (parseFloat(String(cols[idxAmount] || '').replace(/[^0-9.-]+/g, '')) || 0) : 0;
        const resultCode = idxResultCode !== -1 ? (cols[idxResultCode] || '') : '';
        parsed.push({ seq, account, debitCredit, amount, resultCode });
      }
      records = parsed;
      // normalize any incoming processingDate still
      usedProcessingDate = normalizeToADDate(usedProcessingDate || '');
    }

    const result = await processFailedAutopayRecords({ records, processingDate: usedProcessingDate || processingDate });
    return NextResponse.json(result);
  } catch (err: any) {
    console.error('Failed to process failed-autopay upload', err);
    return NextResponse.json({ success: false, message: 'Server error: ' + (err?.message || String(err)) }, { status: 500 });
  }
}
