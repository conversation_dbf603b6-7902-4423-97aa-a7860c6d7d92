import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";

type VehiclePayload = {
  licensePlate: string;
  vehicleType: string;
  brand?: string | null;
  model?: string | null;
  color?: string | null;
  parkingSpaceId: string;
  residentId: string;
  annualFee?: number | null;
  paidDate?: string | null;
  expiryDate?: string | null;
  notes?: string | null;
  isActive?: boolean;
  parkingFeePayer?: 'OWNER' | 'RENTER' | 'CUSTOM';
};

async function checkWorkspaceMembership(userId: string, workspaceId: string) {
  const member = await prisma.member.findFirst({
    where: {
      userId,
      workspaceId,
    },
  });
  return member;
}
export async function getVehicleRegistrations(workspaceId: string) {
  // Basic list; authorization is expected at route level
  const items = await prisma.vehicleRegistration.findMany({
    where: { workspaceId },
    orderBy: { createdAt: "desc" },
  });
  return items;
}

export async function createVehicleRegistration(
  workspaceId: string,
  payload: VehiclePayload
) {
  const { userId } = await auth();
  if (!userId) throw new Error("Unauthorized");

  const member = await checkWorkspaceMembership(userId, workspaceId);
  if (!member) throw new Error("Unauthorized");

  const created = await prisma.vehicleRegistration.create({
    data: {
      licensePlate: payload.licensePlate,
      vehicleType: payload.vehicleType as any,
      brand: payload.brand || null,
      model: payload.model || null,
      color: payload.color || null,
      parkingSpaceId: payload.parkingSpaceId,
      residentId: payload.residentId,
      annualFee: payload.annualFee ?? undefined,
      paidDate: payload.paidDate ? new Date(payload.paidDate) : undefined,
      expiryDate: payload.expiryDate ? new Date(payload.expiryDate) : undefined,
      parkingFeePayer: payload.parkingFeePayer as any,
      workspaceId,
      isActive: payload.isActive ?? true,
    },
  });

  return created;
}

export async function updateVehicleRegistration(
  workspaceId: string,
  id: string,
  payload: Partial<VehiclePayload>
) {
  const { userId } = await auth();
  if (!userId) throw new Error("Unauthorized");

  const member = await checkWorkspaceMembership(userId, workspaceId);
  if (!member) throw new Error("Unauthorized");

  const updated = await prisma.vehicleRegistration.updateMany({
    where: { id, workspaceId },
    data: {
      licensePlate: payload.licensePlate,
      vehicleType: payload.vehicleType as any,
      brand: payload.brand ?? undefined,
      model: payload.model ?? undefined,
      color: payload.color ?? undefined,
      parkingSpaceId: payload.parkingSpaceId ?? undefined,
      residentId: payload.residentId ?? undefined,
      annualFee: payload.annualFee ?? undefined,
      paidDate: payload.paidDate ? new Date(payload.paidDate) : undefined,
      expiryDate: payload.expiryDate ? new Date(payload.expiryDate) : undefined,
      parkingFeePayer: payload.parkingFeePayer as any,
      isActive: payload.isActive ?? undefined,
      updatedAt: new Date(),
    },
  });

  return updated.count > 0;
}

export async function deleteVehicleRegistration(
  workspaceId: string,
  id: string
) {
  const { userId } = await auth();
  if (!userId) throw new Error("Unauthorized");

  const member = await checkWorkspaceMembership(userId, workspaceId);
  if (!member) throw new Error("Unauthorized");

  const del = await prisma.vehicleRegistration.deleteMany({
    where: { id, workspaceId },
  });
  return del.count > 0;
}
