// src/app/api/workspaces/[workspaceId]/parking-spaces/[id]/route.ts
import { NextResponse } from 'next/server';
import { updateParkingSpace, deleteParkingSpace } from '@/actions/parkingSpaces/actions';

export async function PUT(req: Request, props: { params: Promise<{ workspaceId: string; id: string }> }) {
  try {
    const { workspaceId, id } = await props.params;
    const body = await req.json();
    const updated = await updateParkingSpace(workspaceId, id, body);
    return NextResponse.json({ success: true, data: updated });
  } catch (err: any) {
    return NextResponse.json({ success: false, error: err.message }, { status: 400 });
  }
}

export async function DELETE(req: Request, props: { params: Promise<{ workspaceId: string; id: string }> }) {
  try {
    const { workspaceId, id } = await props.params;
    const deleted = await deleteParkingSpace(workspaceId, id);
    return NextResponse.json({ success: true, data: deleted });
  } catch (err: any) {
    return NextResponse.json({ success: false, error: err.message }, { status: 500 });
  }
}
