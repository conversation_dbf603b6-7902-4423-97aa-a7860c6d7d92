
# Add CRUD for ParkingSpace (UI & Actions)

## Summary

This proposal outlines the implementation of full CRUD (Create, Read, Update, Delete) functionality for `ParkingSpace` entities. This includes creating new server actions for data manipulation, exposing these actions via dedicated API routes, and developing a user-friendly frontend interface for managing parking spaces.

## Problem

Currently, there is no direct interface or established backend mechanism to manage `ParkingSpace` records within the application. This gap prevents administrators or authorized users from adding, viewing, modifying, or removing parking space information, hindering the comprehensive management of residential or workspace parking facilities.

## Proposed Solution

We will introduce a set of server actions, API routes, and a UI page to enable complete management of `ParkingSpace` entities.

### 1. Schema (Prisma)

The `ParkingSpace` model already exists in `prisma/schema.prisma`. We will leverage this existing schema without modification for this proposal.

```prisma
model ParkingSpace {
  id            String  @id @default(cuid())
  spaceNumber   String  @map("space_number") // 車位號碼 (e.g., "21", "22")
  spaceType     ParkingSpaceType @default(CAR) @map("space_type")
  
  // Link to resident (OWNER of the parking space)
  residentId    String  @map("resident_id")
  resident      Resident @relation(fields: [residentId], references: [id], onDelete: Cascade)
  
  workspaceId   String
  workspace     Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  isActive      Boolean @default(true) @map("is_active")
  notes         String?
  
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")
  
  vehicleRegistrations VehicleRegistration[]
  parkingRentals       ParkingRental[]

  @@unique([spaceNumber, workspaceId])
  @@index([workspaceId, residentId])
  @@index([workspaceId, spaceType])
  @@map("parking_spaces")
}

enum ParkingSpaceType {
  CAR           // 汽車位
  MOTORCYCLE    // 機車位
  BICYCLE       // 自行車位
  
  @@map("parking_space_types")
}
```

### 2. Backend (Actions & API Routes)

**a) New Actions File: `src/actions/parkingSpaces/actions.ts`**

This file will contain server-side functions for `ParkingSpace` CRUD operations, including authorization checks.

```typescript
// src/actions/parkingSpaces/actions.ts
import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";

type ParkingSpacePayload = {
  spaceNumber: string;
  spaceType: 'CAR' | 'MOTORCYCLE' | 'BICYCLE';
  residentId: string;
  isActive?: boolean;
  notes?: string | null;
};

async function checkWorkspaceMembership(userId: string, workspaceId: string) {
  const member = await prisma.member.findFirst({
    where: {
      userId,
      workspaceId,
    },
  });
  return member;
}

export async function getParkingSpaces(workspaceId: string) {
  const items = await prisma.parkingSpace.findMany({
    where: { workspaceId },
    include: { resident: { select: { name: true, residentId: true } } },
    orderBy: { spaceNumber: "asc" },
  });
  return items;
}

export async function createParkingSpace(
  workspaceId: string,
  payload: ParkingSpacePayload
) {
  const { userId } = await auth();
  if (!userId) throw new Error("Unauthorized");

  const member = await checkWorkspaceMembership(userId, workspaceId);
  if (!member) throw new Error("Unauthorized");

  const created = await prisma.parkingSpace.create({
    data: {
      spaceNumber: payload.spaceNumber,
      spaceType: payload.spaceType,
      residentId: payload.residentId,
      workspaceId,
      isActive: payload.isActive ?? true,
      notes: payload.notes || null,
    },
  });
  return created;
}

export async function updateParkingSpace(
  workspaceId: string,
  id: string,
  payload: Partial<ParkingSpacePayload>
) {
  const { userId } = await auth();
  if (!userId) throw new Error("Unauthorized");

  const member = await checkWorkspaceMembership(userId, workspaceId);
  if (!member) throw new Error("Unauthorized");

  const updated = await prisma.parkingSpace.updateMany({
    where: { id, workspaceId },
    data: {
      spaceNumber: payload.spaceNumber ?? undefined,
      spaceType: payload.spaceType ?? undefined,
      residentId: payload.residentId ?? undefined,
      isActive: payload.isActive ?? undefined,
      notes: payload.notes ?? undefined,
      updatedAt: new Date(),
    },
  });
  return updated.count > 0;
}

export async function deleteParkingSpace(
  workspaceId: string,
  id: string
) {
  const { userId } = await auth();
  if (!userId) throw new Error("Unauthorized");

  const member = await checkWorkspaceMembership(userId, workspaceId);
  if (!member) throw new Error("Unauthorized");

  const del = await prisma.parkingSpace.deleteMany({
    where: { id, workspaceId },
  });
  return del.count > 0;
}
```

**b) New API Routes**

These routes will use the actions defined above.

*   **`src/app/api/workspaces/[workspaceId]/parking-spaces/route.ts`**
    ```typescript
    // src/app/api/workspaces/[workspaceId]/parking-spaces/route.ts
    import { NextResponse } from 'next/server';
    import { getParkingSpaces, createParkingSpace } from '@/actions/parkingSpaces/actions';

    export async function GET(req: Request, props: { params: Promise<{ workspaceId: string }> }) {
      try {
        const { workspaceId } = await props.params;
        const items = await getParkingSpaces(workspaceId);
        return NextResponse.json({ success: true, data: items });
      } catch (err: any) {
        return NextResponse.json({ success: false, error: err.message }, { status: 500 });
      }
    }

    export async function POST(req: Request, props: { params: Promise<{ workspaceId: string }> }) {
      try {
        const { workspaceId } = await props.params;
        const body = await req.json();
        const created = await createParkingSpace(workspaceId, body);
        return NextResponse.json({ success: true, data: created });
      } catch (err: any) {
        return NextResponse.json({ success: false, error: err.message }, { status: 400 });
      }
    }
    ```

*   **`src/app/api/workspaces/[workspaceId]/parking-spaces/[id]/route.ts`**
    ```typescript
    // src/app/api/workspaces/[workspaceId]/parking-spaces/[id]/route.ts
    import { NextResponse } from 'next/server';
    import { updateParkingSpace, deleteParkingSpace } from '@/actions/parkingSpaces/actions';

    export async function PUT(req: Request, props: { params: Promise<{ workspaceId: string; id: string }> }) {
      try {
        const { workspaceId, id } = await props.params;
        const body = await req.json();
        const updated = await updateParkingSpace(workspaceId, id, body);
        return NextResponse.json({ success: true, data: updated });
      } catch (err: any) {
        return NextResponse.json({ success: false, error: err.message }, { status: 400 });
      }
    }

    export async function DELETE(req: Request, props: { params: Promise<{ workspaceId: string; id: string }> }) {
      try {
        const { workspaceId, id } = await props.params;
        const deleted = await deleteParkingSpace(workspaceId, id);
        return NextResponse.json({ success: true, data: deleted });
      } catch (err: any) {
        return NextResponse.json({ success: false, error: err.message }, { status: 500 });
      }
    }
    ```

### 3. Frontend (UI)

**New UI Page: `src/app/(main)/workspaces/[workspaceId]/parking-spaces/page.tsx`**

This page will provide the interface for listing, adding, editing, and deleting parking spaces. It will reuse UI components and patterns from existing pages like `vehicle-registrations/page.tsx`.

```tsx
// src/app/(main)/workspaces/[workspaceId]/parking-spaces/page.tsx
"use client";

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useState, use, useEffect } from 'react'; // Added useEffect
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

// Reusing Resident and ParkingSpaceType from schema for type safety
type ResidentOption = {
  id: string;
  name: string;
  residentId: string; // The property unit ID, e.g., "06-01-1"
};

type ParkingSpace = {
  id: string;
  spaceNumber: string;
  spaceType: 'CAR' | 'MOTORCYCLE' | 'BICYCLE';
  residentId: string;
  resident?: { name: string; residentId: string }; // Include resident info for display
  isActive?: boolean;
  notes?: string | null;
};

// Form component for creating/editing Parking Spaces
function ParkingSpaceForm({ initial, onClose, workspaceId, onSaved }: { initial?: Partial<ParkingSpace> | null; onClose: () => void; workspaceId: string; onSaved: () => void; }) {
  const { register, handleSubmit, setValue, watch, control } = useForm<ParkingSpace>({
    defaultValues: {
      spaceNumber: initial?.spaceNumber || '',
      spaceType: initial?.spaceType || 'CAR',
      residentId: initial?.residentId || '',
      isActive: initial?.isActive ?? true,
      notes: initial?.notes || '',
    },
  });

  const queryClient = useQueryClient();

  // Fetch residents for dropdown
  const { data: residents, isLoading: isLoadingResidents } = useQuery<ResidentOption[]>({
    queryKey: ['residents', workspaceId],
    queryFn: async () => {
      const res = await fetch(`/api/workspaces/${workspaceId}/residents`);
      if (!res.ok) throw new Error('Failed to fetch residents');
      const json = await res.json();
      if (!json.success) throw new Error(json.error || 'Failed to fetch residents');
      // Assuming API returns an array of { id, name, residentId }
      return json.data.map((r: any) => ({ id: r.id, name: `${r.name} (${r.residentId})`, residentId: r.id }));
    },
    enabled: !!workspaceId,
  });

  useEffect(() => {
    // If editing and initial residentId is available, ensure it's selected
    if (initial?.residentId && residents && !watch('residentId')) {
      setValue('residentId', initial.residentId);
    }
  }, [initial, residents, setValue, watch]);


  const createMut = useMutation({
    mutationFn: async (payload: any) => {
      const res = await fetch(`/api/workspaces/${workspaceId}/parking-spaces`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
      if (!res.ok) throw new Error('Failed to create parking space');
      return res.json();
    },
    onSuccess: () => { toast.success('Created'); queryClient.invalidateQueries({ queryKey: ['parking-spaces', workspaceId] }); onSaved(); },
    onError: (err: any) => toast.error(err.message || 'Failed'),
  });

  const updateMut = useMutation({
    mutationFn: async ({ id, payload }: { id: string; payload: any }) => {
      const res = await fetch(`/api/workspaces/${workspaceId}/parking-spaces/${id}`, { method: 'PUT', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
      if (!res.ok) throw new Error('Failed to update');
      return res.json();
    },
    onSuccess: () => { toast.success('Updated'); queryClient.invalidateQueries({ queryKey: ['parking-spaces', workspaceId] }); onSaved(); },
    onError: (err: any) => toast.error(err.message || 'Failed'),
  });

  const onSubmit = handleSubmit((vals) => {
    const payload = { ...vals, residentId: vals.residentId }; // Ensure residentId is passed correctly
    if (initial && initial.id) {
      updateMut.mutate({ id: initial.id!, payload });
    } else {
      createMut.mutate(payload);
    }
  });

  return (
    <form onSubmit={onSubmit} className="space-y-3">
      <div>
        <Label htmlFor="spaceNumber">Space Number</Label>
        <Input id="spaceNumber" {...register('spaceNumber')} required />
      </div>
      <div>
        <Label htmlFor="spaceType">Space Type</Label>
        <Select
          value={watch('spaceType')}
          onValueChange={(value) => setValue('spaceType', value as ParkingSpace['spaceType'])}
        >
          <SelectTrigger id="spaceType"><SelectValue /></SelectTrigger>
          <SelectContent>
            <SelectItem value="CAR">Car</SelectItem>
            <SelectItem value="MOTORCYCLE">Motorcycle</SelectItem>
            <SelectItem value="BICYCLE">Bicycle</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div>
        <Label htmlFor="residentId">Owner (Resident)</Label>
        {isLoadingResidents ? (
          <div>Loading residents...</div>
        ) : (
          <Select
            value={watch('residentId')}
            onValueChange={(value) => setValue('residentId', value)}
          >
            <SelectTrigger id="residentId"><SelectValue /></SelectTrigger>
            <SelectContent>
              {residents?.map((resident) => (
                <SelectItem key={resident.id} value={resident.id}>
                  {resident.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>
      <div>
        <Label htmlFor="notes">Notes</Label>
        <Input id="notes" {...register('notes')} />
      </div>
      {/* isActive checkbox can be added here if needed */}

      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={onClose}>Cancel</Button>
        <Button type="submit">Save</Button>
      </div>
    </form>
  );
}

// Main page component
export default function ParkingSpacesPage(props: { params: Promise<{ workspaceId: string }> }) {
  const params = use(props.params);
  const workspaceId = params.workspaceId;
  const [open, setOpen] = useState(false);
  const [editing, setEditing] = useState<ParkingSpace | null>(null);

  const { data, isLoading, error } = useQuery<ParkingSpace[]>({
    queryKey: ['parking-spaces', workspaceId],
    queryFn: async () => {
      const res = await fetch(`/api/workspaces/${workspaceId}/parking-spaces`);
      if (!res.ok) throw new Error('Failed to fetch parking spaces');
      const json = await res.json();
      if (!json.success) throw new Error(json.error || 'Failed to fetch parking spaces');
      return json.data;
    },
    enabled: !!workspaceId,
  });

  const queryClient = useQueryClient();
  const deleteMut = useMutation({
    mutationFn: async (id: string) => {
      const res = await fetch(`/api/workspaces/${workspaceId}/parking-spaces/${id}`, { method: 'DELETE' });
      if (!res.ok) throw new Error('Failed to delete parking space');
      return res.json();
    },
    onSuccess: () => { toast.success('Deleted'); queryClient.invalidateQueries({ queryKey: ['parking-spaces', workspaceId] }); },
    onError: (err: any) => toast.error(err.message || 'Failed'),
  });

  const handleNew = () => { setEditing(null); setOpen(true); };
  const handleEdit = (item: ParkingSpace) => { setEditing(item); setOpen(true); };
  const handleDelete = (id: string) => { if (confirm('Are you sure you want to delete this parking space?')) deleteMut.mutate(id); };

  if (error) return <div className="text-red-500">Error: {error.message}</div>;

  return (
    <Card className="w-full max-w-5xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Parking Spaces</CardTitle>
          <Button onClick={handleNew}>Add Parking Space</Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading && <div>Loading parking spaces...</div>}
        {!isLoading && data && data.length === 0 && <div>No parking spaces found.</div>}
        {!isLoading && data && data.length > 0 && (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Space Number</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Owner</TableHead>
                <TableHead>Notes</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.map((space) => (
                <TableRow key={space.id}>
                  <TableCell>{space.spaceNumber}</TableCell>
                  <TableCell className="capitalize">{space.spaceType.toLowerCase()}</TableCell>
                  <TableCell>{space.resident?.name || 'N/A'} ({space.resident?.residentId || 'N/A'})</TableCell>
                  <TableCell>{space.notes || '-'}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button variant="ghost" size="sm" onClick={() => handleEdit(space)}>Edit</Button>
                      <Button variant="destructive" size="sm" onClick={() => handleDelete(space.id)}>Delete</Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{editing ? 'Edit Parking Space' : 'Add New Parking Space'}</DialogTitle>
          </DialogHeader>
          <ParkingSpaceForm initial={editing} onClose={() => setOpen(false)} workspaceId={workspaceId} onSaved={() => setOpen(false)} />
        </DialogContent>
      </Dialog>
    </Card>
  );
}
```
