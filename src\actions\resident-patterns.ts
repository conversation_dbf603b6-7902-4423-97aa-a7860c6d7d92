"use server";

import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";
import { revalidatePath } from "next/cache";

async function checkWorkspaceMembership(userId: string, workspaceId: string) {
    const member = await prisma.member.findFirst({
        where: {
            userId,
            workspaceId,
        },
    });
    return member;
}

export async function getResidentPatterns(workspaceId: string) {
    const { userId } = await auth();

    if (!userId) {
        throw new Error("Not authenticated");
    }

    const member = await checkWorkspaceMembership(userId, workspaceId);
    if (!member) {
        throw new Error("Not a member of this workspace");
    }

    return prisma.residentPattern.findMany({
        where: {
            workspaceId,
        },
        orderBy: {
            residentId: "asc",
        },
    });
}

export async function createResidentPattern(data: {
    residentId: string;
    patterns: string;
    workspaceId: string;
}) {
    const { userId } = await auth();

    if (!userId) {
        throw new Error("Not authenticated");
    }

    const { residentId, patterns, workspaceId } = data;
    const member = await checkWorkspaceMembership(userId, workspaceId);
    if (!member) {
        throw new Error("Not a member of this workspace");
    }

    const patternList = patterns.split(",").map((p) => p.trim()).filter(Boolean);

    if (!residentId || patternList.length === 0) {
        throw new Error("Resident ID and at least one pattern are required.");
    }

    const newPattern = await prisma.residentPattern.create({
        data: {
            workspaceId,
            memberId: member.id,
            residentId,
            patterns: patternList,
        },
    });

    revalidatePath(`/deposits/settings/resident-patterns?workspaceId=${workspaceId}`);

    return newPattern;
}

export async function createResidentPatternAction(prevState: any, formData: FormData) {
    const { userId } = await auth();

    if (!userId) {
        return { message: "Not authenticated" };
    }

    try {
        const residentId = formData.get("residentId") as string;
        const patterns = (formData.get("patterns") as string)
            .split(",")
            .map((p) => p.trim())
            .filter(Boolean);
        const workspaceId = formData.get("workspaceId") as string;

        const member = await checkWorkspaceMembership(userId, workspaceId);
        if (!member) {
            return { message: "Not a member of this workspace" };
        }

        if (!residentId || patterns.length === 0) {
            return { message: "Resident ID and at least one pattern are required." };
        }

        await prisma.residentPattern.create({
            data: {
                workspaceId,
                memberId: member.id,
                residentId,
                patterns,
            },
        });

        revalidatePath(`/deposits/settings/resident-patterns?workspaceId=${workspaceId}`);
        return { message: "Pattern created successfully." };
    } catch (error: any) {
        return { message: `Failed to create pattern: ${error.message}` };
    }
}

export async function updateResidentPattern(
    id: string,
    data: {
        residentId: string;
        patterns: string[];
        workspaceId: string;
    }
) {
    const { userId } = await auth();

    if (!userId) {
        throw new Error("Not authenticated");
    }

    const { residentId, patterns, workspaceId } = data;

    const member = await checkWorkspaceMembership(userId, workspaceId);
    if (!member) {
        throw new Error("Not a member of this workspace");
    }

    if (!residentId || !patterns || patterns.length === 0) {
        throw new Error("Resident ID and at least one pattern are required.");
    }

    const updatedPattern = await prisma.residentPattern.update({
        where: {
            id,
            workspaceId,
        },
        data: {
            residentId,
            patterns,
        },
    });

    revalidatePath(`/deposits/settings/resident-patterns?workspaceId=${workspaceId}`);

    return updatedPattern;
}

export async function deleteResidentPattern(id: string, workspaceId: string) {
    const { userId } = await auth();

    if (!userId) {
        throw new Error("Not authenticated");
    }

    const member = await checkWorkspaceMembership(userId, workspaceId);
    if (!member) {
        throw new Error("Not a member of this workspace");
    }

    const deletedPattern = await prisma.residentPattern.delete({
        where: {
            id,
            workspaceId,
        },
    });

    revalidatePath(`/deposits/settings/resident-patterns?workspaceId=${workspaceId}`);

    return deletedPattern;
}
