import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

const isPublicRoute = createRouteMatcher([
  "/",
  "/blog(.*)",
  "/sign-in(.*)",
  "/sign-up(.*)",
  "/pricing(.*)",
  "/customers(.*)",
  "/changelog(.*)",
  "/help(.*)",
  "/oauth-callback",
  "/features(.*)",
  "/api/inngest(.*)",
  "/api/workflows/reminding",
  "/api/auth/google-callback",
  "/api/webhook/line-message",
  "/api/webhook/auto-pay/resident",
  "/api/webhook/setup-richmenu",
  "/api/webhook/setup-richmenu/check-env"
]);

async function handleRequest(auth: any, req: NextRequest) {
  const response = NextResponse.next();

  // For Google callback, ensure we preserve the oauth_state cookie
  if (req.nextUrl.pathname === '/api/auth/google-callback') {
    const state = req.cookies.get('oauth_state');
    if (state) {
      // Preserve the state cookie in the response
      response.cookies.set({
        name: 'oauth_state',
        value: state.value,
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        path: '/',
        maxAge: 60 * 5 // 5 minutes
      });
    }
  }

  if (!isPublicRoute(req)) {
    await auth.protect();
  }

  return response;
}

export default clerkMiddleware(handleRequest);

export const config = {
  matcher: [
    "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)",
    "/(api|trpc)(.*)",
  ],
};