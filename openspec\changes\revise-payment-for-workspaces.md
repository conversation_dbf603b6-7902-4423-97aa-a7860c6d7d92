---
title: Revise Payment Services for Workspaces
status: proposed
---

### Summary

This proposal outlines the necessary changes to integrate Workspace and Member controls into the payment services, including `PaymentRequest`, `Payment`, and `PaymentAccount`. This will align the payment-related schemas and functions with the multi-tenant architecture already implemented for features like `ResidentPattern`.

### Motivation

Currently, the payment services are not workspace-aware, meaning all data is global. This poses a security risk and limits the feature's usability in a multi-workspace environment. By adding `workspaceId` and `memberId` to the relevant models, we can enforce data isolation and track ownership, ensuring users can only access data within their own workspace.

### Proposed Changes

#### 1. Schema Modifications (`prisma/schema.prisma`)

We will add `workspaceId` and `memberId` relations to the `PaymentRequest` and `PaymentAccount` models. This ensures that every payment request and payment account is associated with a specific workspace and a member who created it.

**Migration Strategy for Existing Data:**

To prevent data loss on existing records, the migration will be handled as follows:
1.  The new `workspaceId` and `memberId` fields will be added as required.
2.  A data migration step will update all existing rows in the `payment_requests` and `payment_accounts` tables to use the following default IDs, as requested:
    *   `workspaceId`: `cm5n9zkc30002mmaj3r399pc0`
    *   `memberId`: `cm5n9zkc30004mmajdgt3k9xr`
3.  The unique constraints on these tables will be updated to include `workspaceId`.

Here is the proposed schema:

```prisma
model PaymentRequest {
  id        String    @id @default(cuid())
  name      String    // 社區名稱
  year      String    // 年度
  month     String    // 月份
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  payments     Payment[]
  // Many-to-many relationship with PaymentAccount
  paymentAccounts PaymentRequestAccount[]

  // ADDED: Workspace and Member relations
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  memberId    String
  member      Member    @relation(fields: [memberId], references: [id], onDelete: Cascade, onUpdate: NoAction, onDelete: NoAction)

  // UPDATED: Unique constraint to be workspace-aware
  @@unique([name, year, month, workspaceId])
  @@map("payment_requests")
}

model PaymentAccount {
  id            String          @id @default(cuid())
  accountType   PaymentAccountType // REMITTER, PAYEE, or BOTH
  bankCode      String          // 銀行代號
  bankName      String          // 銀行名稱
  branchName    String          // 分行名稱
  accountNumber String          // 帳號
  accountName   String          // 戶名
  isDefault     Boolean         @default(false) // 是否為預設帳戶
  isActive      Boolean         @default(true)  // 是否啟用

  // Many-to-many relationship with PaymentRequest
  paymentRequests PaymentRequestAccount[]

  // For payments using this account
  paymentsAsRemitter Payment[] @relation("PaymentRemitterAccount")
  paymentsAsPayee    Payment[] @relation("PaymentPayeeAccount")

  // ADDED: Workspace and Member relations
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  memberId    String
  member      Member    @relation(fields: [memberId], references: [id], onDelete: Cascade, onUpdate: NoAction, onDelete: NoAction)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // UPDATED: Unique constraint to be workspace-aware
  @@unique([bankCode, accountNumber, workspaceId])
  @@map("payment_accounts")
}
```

#### 2. API and Action Modifications

All server actions and API endpoints related to `PaymentRequest`, `Payment`, and `PaymentAccount` must be updated to be workspace-aware.

-   **Create Operations:** When creating new entities, the `workspaceId` and `memberId` must be passed from the authenticated user's session.
-   **Read/Update/Delete Operations:** All queries must be filtered by the `workspaceId` from the user's session to ensure they can only access resources within their workspace.

This will involve updating functions in the following files:

-   `src/hooks/use-payment-requests.ts`
-   `src/actions/payments/get-payment-accounts.ts`
-   `src/actions/payments/crud-payment-requests.ts` (or similar for CRUD operations)
-   `pages/api/payments.ts`

These functions will need to be modified to accept `workspaceId` and apply it as a filter in their database queries.

### Next Steps

1.  Apply the schema changes to `prisma/schema.prisma`.
2.  Generate and run the database migration, including the data migration script to populate `workspaceId` and `memberId` for existing records.
3.  Update all relevant server actions and API endpoints to enforce workspace isolation.
4.  Update the frontend components and hooks to provide the necessary `workspaceId` to the backend.
