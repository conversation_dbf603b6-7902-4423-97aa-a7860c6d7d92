import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";
import { Decimal } from "@prisma/client/runtime/library";
import { suggestCategory } from "@/lib/utils/transaction-categorization";
import { 
  calculateTransactionFullHash
 } from "@/lib/utils/hash-calculation";

type CsvRow = {
  交易日期: string;
  交易資訊: string;
  帳務日期: string;
  說明: string;
  提出: string;
  存入: string;
  餘額: string;
  備註: string;
};

type MonthBalance = {
  date: string;
  balance: number;
  fullDateTime: string;
  rawData: CsvRow;
  isLastDayOfMonth: boolean;
}

type MonthlyTotals = {
  [yearMonth: string]: {
    deposits: number;
    withdrawals: number;
  };
}

const WITHDRAWALS_SPECIAL_KEYWORDS = [
  { keyword: "東聯", shouldAccumulate: true },
  { keyword: "大昆", shouldAccumulate: false }, // One-time work
  { keyword: "清潔", shouldAccumulate: true },
  { keyword: "明陽", shouldAccumulate: false }, // One-time maintenance
  { keyword: "強迅", shouldAccumulate: false }, // One-time maintenance
  { keyword: "日立", shouldAccumulate: true },
  { keyword: "東聯保全", shouldAccumulate: true },
  { keyword: "社區清潔", shouldAccumulate: true },
  { keyword: "零用金", shouldAccumulate: true },
  { keyword: "社區公共意外險", shouldAccumulate: true },
  { keyword: "電話費", shouldAccumulate: true },
  { keyword: "電費", shouldAccumulate: true },
  { keyword: "水費", shouldAccumulate: true },
  { keyword: "區權會出席費", shouldAccumulate: true },
  { keyword: "退裝潢保證金", shouldAccumulate: false },
  { keyword: "****************", shouldAccumulate: true },
  { keyword: "委員代墊款", shouldAccumulate: false },
  { keyword: "冠福", shouldAccumulate: false }, // Add this if not already there
];

// ---  將可歸類支出匯入 bankAccount.expense 資料表 ---
export async function createExpenseFromWithdrawals(
  withdrawalsData: Array<{
    transactionDate: string;
    type: "DEBIT" | "CREDIT";
    remark: string;
    amount: string | number;
    row: CsvRow;
  }>,
  accountId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { userId } = await auth();
    if (!userId) throw new Error("未經授權");

    // Account mapping based on pattern
    const managementAccountId = process.env.VIRTUAL_ACCOUNT_ID_MANAGEMENT;
    const parkingAccountId = process.env.VIRTUAL_ACCOUNT_ID_CAR_PARKING_SPACE;

    if (!managementAccountId || !parkingAccountId) {
      throw new Error(
        "Virtual account IDs not configured in environment variables"
      );
    }

    const targetAccountId = accountId.includes("************")
      ? managementAccountId
      : parkingAccountId;

    // Process all withdrawals and prepare the data first
    const preparedData = await Promise.all(
      withdrawalsData
        .filter((withdrawal) => withdrawal.remark !== "−") // Filter out transactions with remark "−"
        .map(async (withdrawal) => {
          const amount =
            typeof withdrawal.amount === "string"
              ? parseFloat(withdrawal.amount.replace(/,/g, ""))
              : withdrawal.amount;

          if (isNaN(amount)) return null;

          // Get category suggestion
          const { categoryId: suggestedCategoryId } = await suggestCategory(
            withdrawal.remark,
            amount,
            "DEBIT"
          );

          // Parse and validate date
          const dateParts = withdrawal.transactionDate.split("/");
          if (dateParts.length !== 3) {
            throw new Error(
              `Invalid date format for monthYear: ${withdrawal.transactionDate}`
            );
          }

          const [year, month, day] = dateParts;
          const parsedYear = parseInt(year);
          const parsedMonth = parseInt(month) - 1;
          const parsedDay = parseInt(day);

          if (isNaN(parsedYear) || isNaN(parsedMonth) || isNaN(parsedDay)) {
            throw new Error(
              `Invalid date components for ${withdrawal.transactionDate}`
            );
          }

          // Parse time components (Taipei time UTC+8)
          let hours = 0,
            minutes = 0;
          if (withdrawal.transactionDate) {
            const [_, time] = withdrawal.transactionDate.split(" ");
            if (time) {
              const [h, m] = time.split(":");
              const parsedHours = parseInt(h);
              const parsedMinutes = parseInt(m);

              if (!isNaN(parsedHours) && !isNaN(parsedMinutes)) {
                hours = parsedHours >= 8 ? parsedHours - 8 : parsedHours + 16;
                minutes = parsedMinutes;
              }
            }
          }

          const date = new Date(
            Date.UTC(parsedYear, parsedMonth, parsedDay, hours, minutes, 0)
          );

          return {
            amount,
            suggestedCategoryId,
            date,
            description: withdrawal.remark,
            transactionDate: withdrawal.transactionDate,
          };
        })
    );

    // Filter out any null values from invalid amounts
    const validData = preparedData.filter(
      (data): data is NonNullable<typeof data> => data !== null
    );

    // First ensure we have a default category
    const defaultCategory = await prisma.category.upsert({
      where: {
        name_userId: {
          name: "OTHER",
          userId,
        },
      },
      update: {},
      create: {
        name: "OTHER",
        userId,
        icon: "HelpCircle",
        type: "DEBIT"
      },
    });

    // Execute all database operations in a single transaction
    //await prisma.$transaction(async (tx) => {
    // Process each withdrawal data
    for (const data of validData) {
      // Generate deduplication hash
      const importHash = calculateTransactionFullHash({
        date: data.transactionDate,
        type: "DEBIT",
        categoryId: defaultCategory.id,
        accountId: targetAccountId,
        amount: data.amount,
        description: data.description,
      });

      // Create expense record if it doesn't exist
      await prisma.expense.upsert({
        where: { importHash },
        update: {}, // No update if exists
        create: {
          amount: new Decimal(data.amount),
          description: data.description,
          date: data.date,
          categoryId: defaultCategory.id,
          userId,
          accountId: targetAccountId,
          type: "DEBIT",
          categoryValidated: true,
          suggestedCategoryId: data.suggestedCategoryId,
          importHash,
        },
      });
    }
    //});

    return { success: true };
  } catch (error) {
    console.error("Error creating expense records:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to create expense records",
    };
  }
}

// --- 將管理費收入匯入 bankAccount.income 資料表 ---
export async function createIncomeFromDeposits(
  depositsData: Array<{
    transactionDate: string;
    type: "DEBIT" | "CREDIT";
    remark: string;
    amount: string | number;
    row: CsvRow;
  }>,
  accountId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { userId } = await auth();
    if (!userId) throw new Error("未經授權");

    // Account mapping based on pattern
    const managementAccountId = process.env.VIRTUAL_ACCOUNT_ID_MANAGEMENT;
    const parkingAccountId = process.env.VIRTUAL_ACCOUNT_ID_CAR_PARKING_SPACE;

    if (!managementAccountId || !parkingAccountId) {
      throw new Error("Virtual account IDs not configured in environment variables");
    }

    const targetAccountId = accountId.includes("************")
      ? managementAccountId
      : parkingAccountId;

    // Process all deposits and prepare the data first
    const preparedData = await Promise.all(
      depositsData
        .filter(deposit => 
          !(deposit.remark === "−" && deposit.row.交易資訊 === "−") || // Keep if at least one field has info
          deposit.row.說明.includes("轉帳") && deposit.row.備註.includes("管理費") || // Keep deposit interest info
          deposit.row.說明.includes("存款息") || // Keep deposit interest info
          deposit.row.備註.includes("信義") || // Keep real estate transactions
          deposit.row.備註.includes("永慶") || // Keep real estate transactions
          deposit.row.備註.includes("全國數位") || // Keep cable media transactions
          deposit.row.交易資訊.includes("168888") || // Keep management committee account
          deposit.row.交易資訊.includes("僑＊福華社區管理委員會") // Keep management committee name
        )
        .map(async (deposit) => {
          const amount =
            typeof deposit.amount === "string"
              ? parseFloat(deposit.amount.replace(/,/g, ""))
              : deposit.amount;

          if (isNaN(amount)) return null;

          // Select the best description based on available information
          let description = deposit.remark !== "−"
            ? deposit.remark
            : deposit.row.交易資訊 !== "−"
              ? deposit.row.交易資訊.trim()
              : deposit.row.說明; // Fallback to 說明 if both are "−"

          console.log("rewrite description============================>", deposit.remark, deposit.row.交易資訊, deposit.row.說明);
          // Rewrite specific descriptions to more readable text
          if (description.includes("全國數位有線電視股份有限公司")) {
            description = "全國數位使用電費";
          }
          if (description.includes("(013)0000025***168888 僑＊福華社區管理委員會")) {
            description = "888-8網銀轉帳";
          }
          if (description.includes("信義")) {
            description = "信義房屋公告費";
          }
          /*if (description.includes("永慶")) {
            description = "永慶房屋公告費";
          }*/


          // Get category suggestion
          const { categoryId: suggestedCategoryId } = await suggestCategory(
            description,
            amount,
            "CREDIT"
          );

          // Parse and validate date
          const dateParts = deposit.row.交易日期.split("/");
          if (dateParts.length !== 3) {
            throw new Error(`Invalid date format for monthYear: ${deposit.row.交易日期}`);
          }

          const [year, month, day] = dateParts;
          const parsedYear = parseInt(year);
          const parsedMonth = parseInt(month) - 1;
          const parsedDay = parseInt(day);

          if (isNaN(parsedYear) || isNaN(parsedMonth) || isNaN(parsedDay)) {
            throw new Error(`Invalid date components for ${deposit.transactionDate}`);
          }

          // Parse time components (Taipei time UTC+8)
          let hours = 0, minutes = 0;
          if (deposit.row.交易日期) {
            const [_, time] = deposit.row.交易日期.split(" ");
            if (time) {
              const [h, m] = time.split(":");
              const parsedHours = parseInt(h);
              const parsedMinutes = parseInt(m);

              if (!isNaN(parsedHours) && !isNaN(parsedMinutes)) {
                hours = parsedHours >= 8 ? parsedHours - 8 : parsedHours + 16;
                minutes = parsedMinutes;
              }
            }
          }

          const date = new Date(
            Date.UTC(parsedYear, parsedMonth, parsedDay, hours, minutes, 0)
          );

          return {
            amount,
            suggestedCategoryId,
            date,
            description,
            transactionDate: deposit.transactionDate
          };
        })
    );

    // Filter out any null values from invalid amounts
    const validData = preparedData.filter((data): data is NonNullable<typeof data> => data !== null);

    // First ensure we have a default category for income
    const defaultCategory = await prisma.category.upsert({
      where: {
        name_userId: {
          name: 'INCOME',
          userId,
        },
      },
      update: {},
      create: {
        name: 'INCOME',
        userId,
        icon: 'Briefcase',
        type: "CREDIT"
      },
    });

    // Process accumulated maintenance fees from globalThis.depositsMaintenanceFee
    if (globalThis.depositsMaintenanceFee) {
      console.log("Processing accumulated maintenance fees:", globalThis.depositsMaintenanceFee);

      // Accumulate totals by month
      const monthlyTotals: { [monthYear: string]: number } = {};

      for (const [, monthlyPayments] of Object.entries(globalThis.depositsMaintenanceFee)) {
        for (const [monthYear, amount] of Object.entries(monthlyPayments)) {
          monthlyTotals[monthYear] = (monthlyTotals[monthYear] || 0) + amount;
        }
      }

      console.log("Monthly maintenance fee totals:", monthlyTotals);

      // Create one income record per month with accumulated total
      for (const [monthYear, totalAmount] of Object.entries(monthlyTotals)) {
        // Extract month number from monthYear (e.g., "2025/06" -> "6")
        const monthMatch = monthYear.match(/(\d{4})\/(\d{2})/);
        if (!monthMatch) continue;

        const year = parseInt(monthMatch[1]);
        const monthNum = parseInt(monthMatch[2]);
        const monthName = monthNum.toString(); // "6" for June

        // Create description with dynamic month
        const description = `${monthName}月份轉帳管理費`;

        // Create date for the last day of the month
        const date = new Date(Date.UTC(year, monthNum, 0, 15, 30, 0)); // Last day of month

        // Get category suggestion
        const { categoryId: suggestedCategoryId } = await suggestCategory(
          description,
          totalAmount,
          "CREDIT"
        );

        // Generate deduplication hash
        const importHash = calculateTransactionFullHash({
          date,
          type: "CREDIT",
          categoryId: defaultCategory.id,
          accountId: targetAccountId,
          amount: totalAmount,
          description,
        });

        // Upsert income record (update if exists, create if not)
        await prisma.income.upsert({
          where: { importHash },
          update: {
            amount: new Decimal(totalAmount),
            description,
            date,
            categoryId: suggestedCategoryId || defaultCategory.id,
            suggestedCategoryId,
            updatedAt: new Date(),
          },
          create: {
            amount: new Decimal(totalAmount),
            description,
            date,
            categoryId: suggestedCategoryId || defaultCategory.id,
            userId,
            accountId: targetAccountId,
            type: "CREDIT",
            categoryValidated: true,
            suggestedCategoryId,
            importHash,
          },
        });

        console.log(`Processed accumulated maintenance fee: ${description} - Total: ${totalAmount}`);
      }
    }

    // Process each deposit data
    console.log("----------------------------preparedData-----------------------------", preparedData)
    console.log("----------------------------validateData-----------------------------", validData)
    for (const data of validData) {
      // Generate deduplication hash
      const importHash = calculateTransactionFullHash({
        date: data.date,
        type: "CREDIT",
        categoryId: defaultCategory.id,
        accountId: targetAccountId,
        amount: data.amount,
        description: data.description,
      });

      // Create income record if it doesn't exist
      await prisma.income.upsert({
        where: { importHash },
        update: {}, // No update if exists
        create: {
          amount: new Decimal(data.amount),
          description: data.description,
          date: data.date,
          categoryId: defaultCategory.id,
          userId,
          accountId: targetAccountId,
          type: "CREDIT",
          categoryValidated: true,
          suggestedCategoryId: data.suggestedCategoryId,
          importHash,
        },
      });
    }

    return { success: true };
  } catch (error) {
    console.error("Error creating income records:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create income records",
    };
  }
}

// --- 將小額收入依金額歸類並匯入 bankAccount.income ---
export async function createIncomeFromSmallDeposits(
  depositsData: Array<{
    transactionDate: string;
    type: "DEBIT" | "CREDIT";
    remark: string;
    amount: string | number;
    row: CsvRow;
  }>,
  accountId: string
): Promise<{ success: boolean; error?: string; unmatched?: any[] }> {
  try {
    const { userId } = await auth();
    if (!userId) throw new Error("未經授權");

    // Account mapping based on pattern
    const managementAccountId = process.env.VIRTUAL_ACCOUNT_ID_MANAGEMENT;
    const parkingAccountId = process.env.VIRTUAL_ACCOUNT_ID_CAR_PARKING_SPACE;

    if (!managementAccountId || !parkingAccountId) {
      throw new Error(
        "Virtual account IDs not configured in environment variables"
      );
    }

    const targetAccountId = accountId.includes("************")
      ? managementAccountId
      : parkingAccountId;

    const unmatchedDeposits: any[] = [];
    
    // Create accumulators for each description type
    const accumulatedDeposits: {
      [key: string]: {
        totalAmount: number;
        date: Date;
        description: string;
        suggestedCategoryId?: string;
        transactions: Array<{
          date: Date;
          amount: number;
          transactionDate: string;
        }>;
      };
    } = {};

    // Separate special deposits from regular small deposits
    const specialDeposits: typeof depositsData = [];
    const regularSmallDeposits: typeof depositsData = [];

    // First pass: Separate special deposits
    for (const deposit of depositsData) {
      const isSpecialDeposit =
        deposit.row.說明.includes("存款息") || // Keep deposit interest info
        deposit.row.備註.includes("信義") || // Keep real estate transactions
        deposit.row.備註.includes("永慶") || // Keep real estate transactions
        deposit.row.備註.includes("全國數位") || // Keep cable media transactions
        deposit.row.交易資訊.includes("168888") || // Keep management committee account
        deposit.row.交易資訊.includes("僑＊福華社區管理委員會"); // Keep management committee name

      // Debug logging for 全國數位 transactions
      if (deposit.row.備註.includes("全國數位")) {
        console.log("[DEBUG] Found 全國數位 transaction:", {
          remark: deposit.row.備註,
          transactionInfo: deposit.row.交易資訊,
          amount: deposit.amount,
          isSpecialDeposit,
          說明: deposit.row.說明
        });
      }

      if (isSpecialDeposit) {
        specialDeposits.push(deposit);
      } else {
        regularSmallDeposits.push(deposit);
      }
    }

    console.log(`Processing ${specialDeposits.length} special deposits and ${regularSmallDeposits.length} regular small deposits`);

    // Process special deposits individually (similar to createIncomeFromDeposits)
    for (const deposit of specialDeposits) {
      const amount = typeof deposit.amount === "string"
        ? parseFloat(deposit.amount.replace(/,/g, ""))
        : deposit.amount;

      if (isNaN(amount)) {
        unmatchedDeposits.push({ ...deposit, reason: "Invalid amount" });
        continue;
      }

      // Select the best description based on available information
      let description = deposit.remark !== "−"
        ? deposit.remark
        : deposit.row.交易資訊 !== "−"
          ? deposit.row.交易資訊.trim()
          : deposit.row.說明; // Fallback to 說明 if both are "−"

      // Rewrite specific descriptions to more readable text
      if (description.includes("全國數位有線電視股份有限公司")) {
        description = "全國數位使用電費";
      }

      // Parse and validate date
      const dateParts = deposit.row.交易日期.split("/");
      if (dateParts.length !== 3) {
        unmatchedDeposits.push({ ...deposit, reason: `Invalid date format: ${deposit.row.交易日期}` });
        continue;
      }

      const [year, month, day] = dateParts;
      const parsedYear = parseInt(year);
      const parsedMonth = parseInt(month) - 1;
      const parsedDay = parseInt(day);

      if (isNaN(parsedYear) || isNaN(parsedMonth) || isNaN(parsedDay)) {
        unmatchedDeposits.push({ ...deposit, reason: `Invalid date components: ${deposit.transactionDate}` });
        continue;
      }

      // Parse time components (Taipei time UTC+8)
      let hours = 0, minutes = 0;
      if (deposit.row.交易日期) {
        const [_, time] = deposit.row.交易日期.split(" ");
        if (time) {
          const [h, m] = time.split(":");
          const parsedHours = parseInt(h);
          const parsedMinutes = parseInt(m);

          if (!isNaN(parsedHours) && !isNaN(parsedMinutes)) {
            hours = parsedHours >= 8 ? parsedHours - 8 : parsedHours + 16;
            minutes = parsedMinutes;
          }
        }
      }

      const date = new Date(Date.UTC(parsedYear, parsedMonth, parsedDay, hours, minutes, 0));

      // Get category suggestion
      const { categoryId: suggestedCategoryId } = await suggestCategory(
        description,
        amount,
        "CREDIT"
      );

      // Create individual income record for special deposit
      const importHash = calculateTransactionFullHash({
        date,
        type: "CREDIT",
        categoryId: suggestedCategoryId || "default",
        accountId: targetAccountId,
        amount,
        description,
      });

      await prisma.income.upsert({
        where: { importHash },
        update: {},
        create: {
          amount: new Decimal(amount),
          description,
          date,
          categoryId: suggestedCategoryId,
          userId,
          accountId: targetAccountId,
          type: "CREDIT",
          categoryValidated: true,
          suggestedCategoryId,
          importHash,
        },
      });

      console.log(`Processed special deposit: ${description} - ${amount}`);
    }

    // Second pass: Process regular small deposits with amount-based categorization
    for (const deposit of regularSmallDeposits) {
      const amount = typeof deposit.amount === "string"
        ? parseFloat(deposit.amount.replace(/,/g, ""))
        : deposit.amount;

      if (isNaN(amount)) {
        unmatchedDeposits.push({ ...deposit, reason: "Invalid amount" });
        continue;
      }

      // Determine description based on amount and remark
      let description;
      if (amount % 500 === 0 && amount <= 2500 && !deposit.remark.includes("臨停")) {
        description = "機車自行車清潔費";
        // Note: Motor bike parking data is now collected in processMotorBikeParkingLot function
      } else if (amount < 700 || deposit.remark.includes("磁扣") || deposit.remark.includes("臨停")) {
        description = "臨時停車費／磁扣";
      //} else if (amount % 500 === 0 && amount <= 2500 && amount !== 974 && amount !== 996) {
      } else {
        unmatchedDeposits.push({
          ...deposit,
          reason: "Amount too large for small deposits"
        });
        continue;
      }

      // Parse date parts
      const dateParts = deposit.row.交易日期.split("/");
      if (dateParts.length !== 3) {
        unmatchedDeposits.push({ ...deposit, reason: "Invalid date format" });
        continue;
      }

      const [year, month, day] = dateParts;
      const parsedYear = parseInt(year);
      const parsedMonth = parseInt(month) - 1;
      const parsedDay = parseInt(day);

      if (isNaN(parsedYear) || isNaN(parsedMonth) || isNaN(parsedDay)) {
        unmatchedDeposits.push({ ...deposit, reason: "Invalid date components" });
        continue;
      }

      // Parse time components (Taipei time UTC+8)
      let hours = 0, minutes = 0;
      if (deposit.row.交易日期) {
        const [_, time] = deposit.row.交易日期.split(" ");
        if (time) {
          const [h, m] = time.split(":");
          const parsedHours = parseInt(h);
          const parsedMinutes = parseInt(m);

          if (!isNaN(parsedHours) && !isNaN(parsedMinutes)) {
            hours = parsedHours >= 8 ? parsedHours - 8 : parsedHours + 16;
            minutes = parsedMinutes;
          }
        }
      }

      const date = new Date(Date.UTC(parsedYear, parsedMonth, parsedDay, hours, minutes, 0));

      // Get category suggestion
      const { categoryId: suggestedCategoryId } = await suggestCategory(
        description,
        amount,
        "CREDIT"
      );

      // Accumulate by description
      if (!accumulatedDeposits[description]) {
        accumulatedDeposits[description] = {
          totalAmount: 0,
          date: date, // Use the first transaction's date
          description,
          suggestedCategoryId,
          transactions: []
        };
      }

      accumulatedDeposits[description].totalAmount += amount;
      accumulatedDeposits[description].transactions.push({
        date,
        amount,
        transactionDate: deposit.transactionDate
      });
    }

    // First ensure we have a default category for income
    const defaultCategory = await prisma.category.upsert({
      where: {
        name_userId: {
          name: 'INCOME',
          userId,
        },
      },
      update: {},
      create: {
        name: 'INCOME',
        userId,
        icon: 'Briefcase',
        type: "CREDIT"
      },
    });

    // Process each accumulated deposit
    for (const [description, data] of Object.entries(accumulatedDeposits)) {
      // Sort transactions by date and use the latest date
      const sortedTransactions = data.transactions.sort((a, b) => b.date.getTime() - a.date.getTime());
      const latestTransaction = sortedTransactions[0];

      // Generate deduplication hash using the accumulated data
      const importHash = calculateTransactionFullHash({
        date: latestTransaction.date,
        type: "CREDIT",
        categoryId: defaultCategory.id,
        accountId: targetAccountId,
        amount: data.totalAmount,
        description: description,
      });

      // Create or update the income record
      await prisma.income.upsert({
        where: { importHash },
        update: {}, // No update if exists
        create: {
          amount: new Decimal(data.totalAmount),
          description: description,
          date: latestTransaction.date,
          categoryId: defaultCategory.id,
          userId,
          accountId: targetAccountId,
          type: "CREDIT",
          categoryValidated: true,
          suggestedCategoryId: data.suggestedCategoryId,
          importHash,
        },
      });
    }

    return {
      success: true,
      unmatched: unmatchedDeposits,
    };
  } catch (error) {
    console.error("Error creating income records:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to create income records",
      unmatched: [],
    };
  }
}

export function calculateMonthlyTotals(records: CsvRow[]): MonthlyTotals {
  const monthlyTotals: MonthlyTotals = {};

  for (const row of records) {
    // Skip rows without transaction date
    if (!row["交易日期"]) continue;

    // Extract year and month from transaction date (format: YYYY/MM/DD)
    const fullDateTimeStr = String(row["交易日期"]);
    const dateStr = fullDateTimeStr.split(/[\s\n]/)[0]; // Get date part only
    const dateParts = dateStr.split('/');
    
    if (dateParts.length < 3) continue; // Need at least year/month/day
    
    const year = parseInt(dateParts[0]);
    const month = parseInt(dateParts[1]);
    
    // Validate year and month
    if (isNaN(year) || isNaN(month) || year < 1900 || year > 2100 || month < 1 || month > 12) {
      console.warn(`Invalid date found: ${row["交易日期"]}, parsed as year: ${year}, month: ${month}`);
      continue;
    }
    
    const yearMonth = `${year}-${String(month).padStart(2, '0')}`;

    // Initialize month entry if it doesn't exist
    if (!monthlyTotals[yearMonth]) {
      monthlyTotals[yearMonth] = {
        deposits: 0,
        withdrawals: 0
      };
    }

    // Add deposit amount
    if (row["存入"] && row["存入"] !== "−") {
      const depositAmount = parseFloat(row["存入"].replace(/,/g, '')) || 0;
      monthlyTotals[yearMonth].deposits += depositAmount;
    }

    // Add withdrawal amount
    if (row["提出"] && row["提出"] !== "−") {
      const withdrawalAmount = parseFloat(row["提出"].replace(/,/g, '')) || 0;
      monthlyTotals[yearMonth].withdrawals += withdrawalAmount;
    }
  }

  return monthlyTotals;
}

// Helper function to find resident by pattern
export async function findResidentByPattern(transactionInfo: string, remark: string, amount: number): Promise<string | null> {
  const { userId } = await auth();
  if (!userId) {
    return null;
  }

  const residentPatterns = await prisma.residentPattern.findMany({
    where: { member: { userId } },
  });

  if (residentPatterns.length === 0) {
    return null;
  }

  const textToSearch = `${transactionInfo || ''} ${remark || ''}`;
  
  console.log("[DEBUG] findResidentByPattern checking:", {
    transactionInfo,
    textToSearch
  });

  for (const resident of residentPatterns) {
    const patterns = resident.patterns.map(p => new RegExp(p.slice(1, p.lastIndexOf('/'))));
    // Check if any of the patterns match
    const hasMatch = patterns.some(pattern => {
      const matches = pattern.test(textToSearch);
      if (resident.residentId === '71-11-3') {
        console.log(`[DEBUG] Testing pattern ${pattern} for resident 71-11-3:`, {
          matches,
          textToSearch
        });
      }
      return matches;
    });

    if (hasMatch) {
      console.log(`[DEBUG] Found match for resident ${resident.residentId}`);
      // Special cases for amount-dependent matching
      if (resident.residentId === '69-11-1' && amount < 1288) continue;
      if (resident.residentId === '69-11-2' && amount < 1396) continue;
      return resident.residentId;
    }
  }
  return null;
}

// Separate processing functions
export function processLastDayBalances(records: CsvRow[]): { [monthYear: string]: MonthBalance } {
  const balances: { [monthYear: string]: MonthBalance } = {};

  console.log("[DEBUG] processLastDayBalances called with", records.length, "records");

  for (const row of records) {
    const transactionDateStr = row["交易日期"];
    if (!transactionDateStr) {
      console.log("[DEBUG] Skipping row - no transaction date:", row);
      continue;
    }

    const fullDateTimeStr = String(transactionDateStr);
    const dateStr = fullDateTimeStr.split(/[\s\n]/)[0];
    const dateParts = dateStr.split("/");

    console.log("[DEBUG] Processing row:", {
      transactionDateStr,
      fullDateTimeStr,
      dateStr,
      dateParts,
      balance: row["餘額"]
    });

    if (dateParts.length !== 3) {
      console.log("[DEBUG] Skipping row - invalid date parts:", dateParts);
      continue;
    }

    const year = parseInt(dateParts[0]);
    const month = parseInt(dateParts[1]);
    const day = parseInt(dateParts[2]);

    if (isNaN(year) || isNaN(month) || isNaN(day)) {
      console.log("[DEBUG] Skipping row - invalid date numbers:", { year, month, day });
      continue;
    }
    
    const monthYearKey = `${year}/${String(month).padStart(2, "0")}`;
    const lastDay = new Date(year, month, 0).getDate();
    const balance = row["餘額"];
    const isLastDayOfMonth = day === lastDay;

    const currentDate = new Date(year, month - 1, day);
    const currentTime = fullDateTimeStr.split(/[\s\n]/)[1];
    console.log("balance", balance, "isLastDayOfMonth", isLastDayOfMonth);
    
    if (currentTime) {
      const [hours, minutes] = currentTime.split(":");
      currentDate.setHours(parseInt(hours), parseInt(minutes));
    }

    const existingBalance = balances[monthYearKey];
    const shouldUpdate = !existingBalance || 
      new Date(existingBalance.fullDateTime) <= currentDate;

    console.log("shouldUpdate===============================>", shouldUpdate);
    console.log("balances", balances);
    console.log("existingBalance", existingBalance);
    console.log("balances[monthYearKey]", balances[monthYearKey]);
    if (shouldUpdate) {
      const balanceNum = typeof balance === "string" 
        ? parseFloat(balance.replace(/,/g, "")) 
        : balance;
        
      if (!isNaN(balanceNum)) {
        balances[monthYearKey] = {
          date: dateStr,
          fullDateTime: fullDateTimeStr,
          balance: balanceNum,
          isLastDayOfMonth,
          rawData: row,
        };
      }
    }
  }

  console.log("[DEBUG] processLastDayBalances returning:", balances);
  console.log("[DEBUG] Number of balance entries found:", Object.keys(balances).length);

  return balances;
}

export function processMotorBikeParkingLot(depositRecords: CsvRow[]): Array<{
  transactionDate: string;
  type: "CREDIT";
  amount: number;
  remark: string;
  row: any;
}> {
  const motorBikeParkingData: Array<{
    transactionDate: string;
    type: "CREDIT";
    amount: number;
    remark: string;
    row: any;
  }> = [];

  // Initialize global array if it doesn't exist
  if (!globalThis.motorBikeParkingLot) globalThis.motorBikeParkingLot = [];

  for (const row of depositRecords) {
    const depositAmountStr = row["存入"];
    const remark = row["備註"];
    const transactionDateStr = row["交易日期"];

    if (!depositAmountStr || depositAmountStr.trim() === "−" || !transactionDateStr) {
      continue;
    }

    try {
      const amount = parseFloat(depositAmountStr.replace(/,/g, ""));

      if (isNaN(amount)) continue;

      // Check if this is motor bike parking fee (機車自行車清潔費)
      if (amount % 500 === 0 && amount <= 2500 && !remark.includes("臨停")) {
        const motorBikeEntry = {
          transactionDate: String(transactionDateStr).split(/[\s\n]/)[0],
          type: "CREDIT" as const,
          amount,
          remark: remark || "",
          row
        };

        motorBikeParkingData.push(motorBikeEntry);
        globalThis.motorBikeParkingLot.push(motorBikeEntry);
        console.log("Added motor bike parking entry:##################################", motorBikeEntry);
      }
    } catch (error) {
      console.error("Error processing motor bike parking deposit:", error);
    }
  }

  console.log("Processed motor bike parking data:", motorBikeParkingData.length, "entries");
  return motorBikeParkingData;
}

export function processWithdrawals(withdrawalRecords: CsvRow[]): Array<{
  transactionDate: string;
  type: "DEBIT";
  remark: string;
  amount: string | number;
  row: CsvRow;
}> {
  const withdrawalsData: Array<{
    transactionDate: string;
    type: "DEBIT";
    remark: string;
    amount: string | number;
    row: CsvRow;
  }> = [];

  // Initialize global arrays if they don't exist
  if (!globalThis.withdrawals) globalThis.withdrawals = [];
  if (!globalThis.withdrawalsUnmatched) globalThis.withdrawalsUnmatched = [];

  // Map to accumulate special withdrawals by date and keyword
  const specialWithdrawalAccumulator = new Map<string, {
    transactionDate: string;
    type: "DEBIT";
    remark: string;
    amount: number;
    rows: CsvRow[];
    count: number;
  }>();

  for (const row of withdrawalRecords) {
    const withdrawAmountStr = row["提出"];
    const remark = row["備註"];
    const transactionInfo = row["交易資訊"];
    const transactionDateStr = row["交易日期"];

    if (!withdrawAmountStr || withdrawAmountStr.trim() === "−" || !transactionDateStr) {
      continue;
    }

    try {
      const dateStr = String(transactionDateStr).split(/[\s\n]/)[0];
      const dateParts = dateStr.split("/");
      
      if (dateParts.length !== 3) continue;
      
      const year = parseInt(dateParts[0]);
      const monthNum = parseInt(dateParts[1]);
      
      if (isNaN(year) || monthNum < 1 || monthNum > 12) continue;

      const amount = parseFloat(withdrawAmountStr.replace(/,/g, ""));

      // Check if this is a special withdrawal
      const textToSearch = `${remark || ''} ${transactionInfo || ''}`;

      // Debug logging for the specific case
      if (textToSearch.includes("****************")) {
        console.log("[DEBUG] Found **************** in transaction:", {
          remark,
          transactionInfo,
          textToSearch,
          withdrawAmountStr,
          dateStr
        });
      }

      let matchedKeyword: string | undefined;
      let shouldAccumulate: boolean = true;

      // Check for special keywords from WITHDRAWALS_SPECIAL_KEYWORDS
      for (const item of WITHDRAWALS_SPECIAL_KEYWORDS) {
        if (textToSearch.includes(item.keyword)) {
          matchedKeyword = item.keyword;
          shouldAccumulate = item.shouldAccumulate; // Capture the shouldAccumulate flag
          break;
        }
      }

      // Special conditions for blank remark and transactionInfo
      // Check for blank fields first
      if (remark.trim() === "−" && transactionInfo.trim() === "−") {
        // Only match amount when both fields are blank
        if (amount === 134000) {
          matchedKeyword = "東聯";
        } else if (amount === 62850) {
          matchedKeyword = "日立";
        } else if (amount === 31000) {
          matchedKeyword = "清潔";
        } else if (amount === 4000) {
          matchedKeyword = "冠福機電保養費";
        }
      } else {
        // Check non-blank remarks for keyword matches (only if not already matched)
        if (!matchedKeyword) {
          if (remark.includes("東聯") || remark.includes("保全")) {
            matchedKeyword = "東聯";
          } else if (remark.includes("日立") || remark.includes("電梯")) {
            matchedKeyword = "日立";
          } else if (remark.includes("清潔") || remark.includes("清理")) {
            matchedKeyword = "清潔";
          //} else if (remark.includes("冠福") || remark.includes("機電")) {
          //  matchedKeyword = "冠福";
          }
        }
      }

      // Debug logging for matching result
      console.log("textToSearch", textToSearch, "matchedKeyword", matchedKeyword)
      if (textToSearch.includes("****************")) {
        console.log("[DEBUG] Matching result for ****************:", {
          matchedKeyword,
          allKeywords: WITHDRAWALS_SPECIAL_KEYWORDS
        });
      }

      if (matchedKeyword) {
        if (!shouldAccumulate) {
          // Don't accumulate - add as unmatched
          const withdrawalData = {
            transactionDate: dateStr,
            type: "DEBIT" as const,
            remark: remark || transactionInfo || "",
            amount: isNaN(amount) ? withdrawAmountStr : amount,
            row,
          };
          globalThis.withdrawalsUnmatched.push(withdrawalData);
          continue; // Skip to next record
        }

        // Special withdrawal - accumulate by date and keyword
        const accumulatorKey = `${dateStr}_${matchedKeyword}`;

        // Rewrite specific keywords to full names
        let displayRemark = matchedKeyword;
        if (matchedKeyword.includes("****************")) {
          displayRemark = "轉汽車專戶7777-7";
        } else if (matchedKeyword === "東聯" || matchedKeyword.includes("東聯")) {
          displayRemark = "東聯保全費";
        } else if (matchedKeyword === "清潔" || matchedKeyword.includes("清潔")) {
          displayRemark = "社區清潔費";
        //} else if (matchedKeyword === "冠福" || matchedKeyword.includes("冠福")) {
        //  displayRemark = "冠福機電保養費";
        } else if (matchedKeyword === "日立" || matchedKeyword.includes("日立")) {
          displayRemark = "日立永大電梯保養費";
        } else if (matchedKeyword.includes("強迅")) {
          displayRemark = "強迅監視系統維修費";
        }

        if (specialWithdrawalAccumulator.has(accumulatorKey)) {
          // Add to existing entry
          const existing = specialWithdrawalAccumulator.get(accumulatorKey)!;
          existing.amount += isNaN(amount) ? 0 : amount;
          existing.rows.push(row);
          existing.count += 1;

          console.log(`[DEBUG] Accumulating ${displayRemark} on ${dateStr}: ${existing.amount} (count: ${existing.count})`);
        } else {
          // Create new entry
          specialWithdrawalAccumulator.set(accumulatorKey, {
            transactionDate: dateStr,
            type: "DEBIT" as const,
            remark: displayRemark,
            amount: isNaN(amount) ? 0 : amount,
            rows: [row],
            count: 1
          });

          console.log(`[DEBUG] New ${displayRemark} entry on ${dateStr}: ${isNaN(amount) ? 0 : amount}`);
        }
      } else {
        // Unmatched withdrawal - add directly
        const withdrawalData = {
          transactionDate: dateStr,
          type: "DEBIT" as const,
          remark: remark || "",
          amount: isNaN(amount) ? withdrawAmountStr : amount,
          row,
        };
        globalThis.withdrawalsUnmatched.push(withdrawalData);
      }
    } catch (error) {
      console.error("Error processing withdrawal row:", row, error);
    }
  }

  // Process accumulated special withdrawals
  for (const [, accumulated] of specialWithdrawalAccumulator.entries()) {
    const withdrawalData = {
      transactionDate: accumulated.transactionDate,
      type: "DEBIT" as const,
      remark: accumulated.count > 1
        ? `${accumulated.remark} (${accumulated.count}筆)`
        : accumulated.remark,
      amount: accumulated.amount,
      row: accumulated.rows[0], // Use the first row as representative
    };

    withdrawalsData.push(withdrawalData);
    globalThis.withdrawals.push(withdrawalData);

    console.log(`[DEBUG] Final accumulated withdrawal: ${accumulated.remark} on ${accumulated.transactionDate} = ${accumulated.amount} (${accumulated.count} transactions)`);
  }

  return withdrawalsData;
}