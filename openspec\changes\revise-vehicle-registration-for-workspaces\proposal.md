
# Revise VehicleRegistration to Support Flexible Fee Payer

## Summary

This proposal outlines the necessary changes to update the `VehicleRegistration` schema to allow for a flexible assignment of the fee payer. This will enable specifying whether the annual fee for a vehicle is to be paid by the parking space `OWNER` or a `RENTER`. The change involves modifying the Prisma schema, updating the vehicle registration management UI, and adjusting the corresponding API endpoints.

## Problem

Currently, the `VehicleRegistration` model does not have an explicit field to determine who is responsible for paying the associated annual fee. The payment responsibility is ambiguous, especially in scenarios where the parking space is rented. The current system makes it difficult to correctly bill for vehicle fees when the payer is not the owner of the parking space.

## Proposed Solution

To resolve this, we will introduce a new field to the `VehicleRegistration` model to explicitly track the designated fee payer.

### 1. Schema Update (`prisma.schema`)

- Add a new field `annualFeePayee` to the `VehicleRegistration` model.
- This field will use the existing `ParkingFeePayee` enum, which includes values like `OWNER`, `RENTER`, and `CUSTOM`.
- The default value for this field will be `OWNER`.

**`prisma/schema.prisma` diff:**
```diff
--- a/prisma/schema.prisma
+++ b/prisma/schema.prisma
@@ -1404,6 +1404,9 @@
   // NOTE: If parking space is rented, payment responsibility transfers to renter
   annualFee       Int?     @map("annual_fee") // 金額 (in cents, null for cars as they use monthly parking fee)
   paidDate        DateTime? @map("paid_date") // 繳費日
   expiryDate      DateTime? @map("expiry_date") // 截止日
+
+  // NEW: Designate who pays the annual fee for this vehicle
+  annualFeePayee  ParkingFeePayee @default(OWNER) @map("annual_fee_payee")
   
   // Registration dates
   registrationDate DateTime @default(now()) @map("registration_date") // 登記日期

```

### 2. Frontend UI Update (`page.tsx`)

- The data type (`VR`) used in the frontend will be updated to include the new `annualFeePayee` field.
- The `VRForm` component will be modified to include a `Select` dropdown for the "Annual Fee Payer", allowing users to choose between `OWNER` and `RENTER`.
- The main vehicle registrations table will be updated to include a "Fee Payer" column to display this information.

**`src/app/(main)/workspaces/[workspaceId]/vehicle-registrations/page.tsx` diff:**
```diff
--- a/src/app/(main)/workspaces/[workspaceId]/vehicle-registrations/page.tsx
+++ b/src/app/(main)/workspaces/[workspaceId]/vehicle-registrations/page.tsx
@@ -6,6 +6,7 @@
 import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
 import { Input } from '@/components/ui/input';
 import { Label } from '@/components/ui/label';
+import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
 import { useForm } from 'react-hook-form';
 import { toast } from 'sonner';
 
@@ -18,12 +19,13 @@
   color?: string | null;
   parkingSpaceId: string;
   residentId: string;
+  annualFeePayee: 'OWNER' | 'RENTER' | 'CUSTOM';
   isActive?: boolean;
 };
 
 function VRForm({ initial, onClose, workspaceId, onSaved }: { initial?: Partial<VR> | null; onClose: () => void; workspaceId: string; onSaved: () => void; }) {
-  const { register, handleSubmit, setValue } = useForm<VR>({
+  const { register, handleSubmit, setValue, watch } = useForm<VR>({
     defaultValues: {
       licensePlate: initial?.licensePlate || '',
       vehicleType: initial?.vehicleType || 'CAR',
@@ -32,6 +34,7 @@
       color: initial?.color || '',
       parkingSpaceId: initial?.parkingSpaceId || '',
       residentId: initial?.residentId || '',
+      annualFeePayee: initial?.annualFeePayee || 'OWNER',
       isActive: initial?.isActive ?? true,
     },
   });
@@ -93,6 +96,17 @@
         <Label>Resident ID</Label>
         <Input {...register('residentId')} />
       </div>
+      <div>
+        <Label>Annual Fee Payer</Label>
+        <Select
+          value={watch('annualFeePayee')}
+          onValueChange={(value) => setValue('annualFeePayee', value as VR['annualFeePayee'])}
+        >
+          <SelectTrigger><SelectValue /></SelectTrigger>
+          <SelectContent>
+            <SelectItem value="OWNER">Owner</SelectItem>
+            <SelectItem value="RENTER">Renter</SelectItem>
+          </SelectContent>
+        </Select>
+      </div>
 
       <div className="flex justify-end space-x-2 pt-4">
         <Button type="button" variant="outline" onClick={onClose}>Cancel</Button>
@@ -148,6 +162,7 @@
                 <TableHead>Brand</TableHead>
                 <TableHead>Model</TableHead>
                 <TableHead>Resident</TableHead>
+                <TableHead>Fee Payer</TableHead>
                 <TableHead className="text-right">Actions</TableHead>
               </TableRow>
             </TableHeader>
@@ -159,6 +174,7 @@
                   <TableCell>{r.brand || '-'}</TableCell>
                   <TableCell>{r.model || '-'}</TableCell>
                   <TableCell>{r.residentId}</TableCell>
+                  <TableCell className="capitalize">{r.annualFeePayee.toLowerCase()}</TableCell>
                   <TableCell className="text-right">
                     <div className="flex justify-end gap-2">
                       <Button variant="ghost" size="sm" onClick={() => handleEdit(r)}>Edit</Button>
```

### 3. API Backend Update

- The API endpoints for creating (`POST /api/workspaces/{workspaceId}/vehicle-registrations`) and updating (`PUT /api/workspaces/{workspaceId}/vehicle-registrations/{id}`) vehicle registrations must be updated.
- The request body will now include the `annualFeePayee` field.
- The backend logic will validate and save this new field to the database using Prisma.

This structured approach ensures that the new business logic is correctly implemented across the database, API, and user interface, providing a clear and manageable system for handling vehicle registration fees.
