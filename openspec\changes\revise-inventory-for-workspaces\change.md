
# Revise Inventory Management for Workspaces

**Objective:** Refactor the inventory management feature to be workspace-aware, ensuring that inventory, sales, residents, and stock movements are all scoped to the user's current workspace.

## 1. Schema Changes (`prisma/schema.prisma`)

### `Inventory` Model
- Add `workspaceId` and a relation to `Workspace`.
- Replace the `@@unique([itemType])` with `@@unique([itemType, workspaceId])`.

```prisma
model Inventory {
  id        String   @id @default(cuid())
  itemType  ItemType @map("item_type")
  current   Int      @default(0)
  price     Int
  minStock  Int      @default(0) @map("min_stock")
  maxStock  Int      @default(100) @map("max_stock")
  
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  saleItems SaleItem[]
  stockMovements StockMovement[]

  @@unique([itemType, workspaceId])
  @@map("inventory")
}
```

### `Resident` Model
- Add `workspaceId` and a relation to `Workspace`.
- Update the unique constraint to be `@@unique([name, address, workspaceId])`.

```prisma
model Resident {
  id      String @id @default(cuid())
  name    String
  address String
  phone   String?
  email   String?
  
  building String?
  floor    String?
  unit     String?
  
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  sales Sale[]
  
  @@unique([name, address, workspaceId])
  @@map("residents")
}
```

### `Sale` Model
- Add `workspaceId` and a relation to `Workspace`.

```prisma
model Sale {
  id        String   @id @default(cuid())
  date      DateTime @default(now())
  receiptNo String?  @unique @map("receipt_no")
  recipient String?
  notes     String?
  totalAmount Int    @map("total_amount")
  
  residentId   String?  @map("resident_id")
  resident     Resident? @relation(fields: [residentId], references: [id])
  residentName String   @map("resident_name")
  address      String
  
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  saleItems SaleItem[]

  @@map("sales")
}
```

### `StockMovement` Model
- Add `workspaceId` and a relation to `Workspace`.
- Add `memberId` and a relation to `Member` to track the user who made the change, replacing the `createdBy` string field.

```prisma
model StockMovement {
  id          String            @id @default(cuid())
  
  inventoryId String            @map("inventory_id")
  inventory   Inventory         @relation(fields: [inventoryId], references: [id])
  
  type        StockMovementType
  quantity    Int
  reason      String?
  reference   String?
  
  previousStock Int @map("previous_stock")
  newStock      Int @map("new_stock")
  
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  memberId    String
  member      Member    @relation(fields: [memberId], references: [id])

  createdAt DateTime @default(now()) @map("created_at")

  @@map("stock_movements")
}
```

## 2. File Structure Change

- Move the inventory page from `src/app/(main)/inventory/page.tsx` to `src/app/(main)/workspaces/[workspaceId]/inventory/page.tsx`.

## 3. Action Changes (`src/actions/inventory/actions.ts`)

- All functions will be updated to accept a `workspaceId` argument.
- All Prisma queries will be filtered by `workspaceId`.
- The `isSuperUser` check will be replaced with role-based access control (e.g., checking if the user is an `ADMIN` of the workspace).
- The `processSale` and `updateInventoryStock` functions will retrieve the `memberId` of the current user for the given workspace and use it to create `StockMovement` records.

## 4. Page Component Changes (`src/app/(main)/workspaces/[workspaceId]/inventory/page.tsx`)

- The page will receive the `workspaceId` from the URL `params`.
- The `workspaceId` will be passed to all the data-fetching and mutation actions from `react-query`.

## 5. Type Changes (`src/types/inventory.ts`)

- The types will be updated to reflect the new schema, although most of the changes are on the backend and may not significantly alter the frontend type definitions.
