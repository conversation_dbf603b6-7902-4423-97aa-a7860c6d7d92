"use client";

import React, { useState, use } from 'react';
import { Button } from "@/components/ui/button"

export default function WorkspaceResidentImportPage(props: {
  params: Promise<{ workspaceId: string }>;
}) {
  const params = use(props.params);
  const { workspaceId } = params;

  const [file, setFile] = useState<File | null>(null);
  const [billingPeriod, setBillingPeriod] = useState('');
  const [preview, setPreview] = useState<any | null>(null);
  const [loading, setLoading] = useState(false);

  async function onUpload(e: React.FormEvent) {
    e.preventDefault();
    if (!file) return alert('Choose a file first');
    if (!workspaceId) return alert('Workspace ID missing in route');
    setLoading(true);
    const fd = new FormData();
    fd.append('file', file);
    fd.append('dryRun', 'true');

    try {
      const res = await fetch(`/api/imports/workspaces/${workspaceId}/residents`, { method: 'POST', body: fd });
      const data = await res.json();
      setPreview(data);
    } catch (err) {
      console.error(err);
      alert('Upload failed');
    } finally {
      setLoading(false);
    }
  }

  async function onImport(e: React.FormEvent) {
    e.preventDefault();
    if (!file) return alert('Choose a file first');
    if (!workspaceId) return alert('Workspace ID missing in route');
    if (!billingPeriod) return alert('Please enter billing period (e.g. 2025-11)');
    setLoading(true);
    const fd = new FormData();
    fd.append('file', file);
    fd.append('dryRun', 'false');
    fd.append('billingPeriod', billingPeriod);

    try {
      const res = await fetch(`/api/imports/workspaces/${workspaceId}/residents`, { method: 'POST', body: fd });
      const data = await res.json();
      setPreview(data);
    } catch (err) {
      console.error(err);
      alert('Import failed');
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">住戶管理費匯入</h1>
      <form onSubmit={onUpload} className="space-y-4">
        <div>
          <label className="block mb-1">Workspace ID</label>
          <input value={workspaceId || ''} disabled className="border p-2 w-full bg-gray-100" />
        </div>
        <div>
          <label className="block mb-1">Billing Period (YYYY-MM)</label>
          <input value={billingPeriod} onChange={e => setBillingPeriod(e.target.value)} className="border p-2 w-full" placeholder="2025-11" />
        </div>
        <div>
          <label className="block mb-1">CSV / XLSX file</label>
          <input type="file" accept=".csv,.xlsx" onChange={e => setFile(e.target.files ? e.target.files[0] : null)} />
        </div>
        <div className="flex gap-2">
          <Button size="sm" className="btn btn-primary" type="submit" disabled={loading || !workspaceId}>{loading ? 'Parsing...' : '預覽'}</Button>
          <Button size="sm" className="btn" onClick={onImport} disabled={loading || !workspaceId}>{loading ? 'Importing...' : '匯入'}</Button>
        </div>
      </form>

      {preview && (
        <div className="mt-6">
          <h2 className="text-xl font-semibold">Preview</h2>
          <pre className="bg-gray-100 p-4 mt-2 overflow-auto max-h-96">{JSON.stringify(preview, null, 2)}</pre>
        </div>
      )}
    </div>
  );
}
