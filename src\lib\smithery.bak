
"server only"
import { createSmitheryUrl } from "@smithery/sdk/shared/config.js";
import { StreamableHTTPClientTransport } from "@modelcontextprotocol/sdk/client/streamableHttp.js"
import { Client } from "@modelcontextprotocol/sdk/client/index.js"

// Smithery client
export async function createSmitheryClient() {
  const config = {
    "googleClientId": process.env.GOOGLE_TASK_CLIENT_ID!,
    "googleClientSecret": process.env.GOOGLE_TASK_CLIENT_SECRET!,
    "googleRefreshToken": process.env.GOOGLE_TASK_REFRESH_TOKEN!,
  };
  const serverUrl = createSmitheryUrl(
    "https://server.smithery.ai/@alvinjchoi/gtasks-mcp", 
    { config, apiKey: process.env.SMITHERY_API_KEY! }
  );
  console.log("serverUrl", serverUrl)


  const transport = new StreamableHTTPClientTransport(serverUrl);
  const client = new Client({
    name: "<PERSON><PERSON>",
    version: "1.0.0"
  });
  
  await client.connect(transport);
  return client.listTools();
}