"use client";

import {
  createResidentPattern,
  deleteResidentPattern,
  updateResidentPattern,
} from "@/actions/resident-patterns";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ResidentPattern } from "@/lib/types";
import { toast } from "sonner";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { SquarePlus, MoreHorizontal } from "lucide-react";
import { useState, useMemo } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

export function ResidentPatternList({
  patterns,
  workspaceId,
}: {
  patterns: ResidentPattern[];
  workspaceId: string;
}) {
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editedPattern, setEditedPattern] = useState<{
    residentId: string;
    patterns: string;
  } | null>(null);
  const [isAdding, setIsAdding] = useState(false);
  const [newPattern, setNewPattern] = useState({
    residentId: "",
    patterns: "",
  });
  const [residentIdFilter, setResidentIdFilter] = useState("");
  const [patternsFilter, setPatternsFilter] = useState("");

  const filteredPatterns = useMemo(() => {
    return patterns.filter((pattern) => {
      const residentIdMatch = pattern.residentId
        .toLowerCase()
        .includes(residentIdFilter.toLowerCase());
      const patternsMatch = pattern.patterns.some((p) =>
        p.toLowerCase().includes(patternsFilter.toLowerCase())
      );
      return residentIdMatch && patternsMatch;
    });
  }, [patterns, residentIdFilter, patternsFilter]);

  const handleDelete = async (id: string) => {
    try {
      await deleteResidentPattern(id, workspaceId);
      toast.success("Pattern deleted successfully.");
    } catch (error) {
      toast.error("Failed to delete pattern.");
    }
  };

  const handleEdit = (pattern: ResidentPattern) => {
    setEditingId(pattern.id);
    setEditedPattern({
      residentId: pattern.residentId,
      patterns: pattern.patterns.join(", "),
    });
  };

  const handleCancel = () => {
    setEditingId(null);
    setEditedPattern(null);
    setIsAdding(false);
    setNewPattern({ residentId: "", patterns: "" });
  };

  const handleSave = async (id: string) => {
    if (!editedPattern) return;

    try {
      await updateResidentPattern(id, {
        residentId: editedPattern.residentId,
        patterns: editedPattern.patterns.split(",").map((p) => p.trim()),
        workspaceId,
      });
      toast.success("Pattern updated successfully.");
      handleCancel();
    } catch (error) {
      toast.error("Failed to update pattern.");
    }
  };

  const handleAdd = async () => {
    try {
      await createResidentPattern({ ...newPattern, workspaceId });
      toast.success("Pattern added successfully.");
      handleCancel();
    } catch (error) {
      toast.error("Failed to add pattern.");
    }
  };

  return (
    <div>
      <div className="flex space-x-4 mb-4">
        <Input
          placeholder="Filter by Resident ID"
          value={residentIdFilter}
          onChange={(e) => setResidentIdFilter(e.target.value)}
        />
        <Input
          placeholder="Filter by Patterns"
          value={patternsFilter}
          onChange={(e) => setPatternsFilter(e.target.value)}
        />
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="px-2 py-0">Resident ID</TableHead>
            <TableHead className="px-2 py-0">Patterns</TableHead>
            <TableHead className="text-right px-2 py-0">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <SquarePlus className="size-8" color={"#1414e7ff"} />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setIsAdding(true)}>
                    Add new pattern
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isAdding && (
            <TableRow>
              <TableCell className="px-2 py-0">
                <Input
                  placeholder="e.g., 06-01-1"
                  value={newPattern.residentId}
                  onChange={(e) =>
                    setNewPattern({ ...newPattern, residentId: e.target.value })
                  }
                />
              </TableCell>
              <TableCell className="px-2 py-0">
                <Textarea
                  placeholder="e.g., /438530/, /another-pattern/"
                  value={newPattern.patterns}
                  onChange={(e) =>
                    setNewPattern({ ...newPattern, patterns: e.target.value })
                  }
                  rows={1}
                />
              </TableCell>
              <TableCell className="text-right space-x-2">
                <Button size="sm" onClick={handleAdd}>
                  Save
                </Button>
                <Button size="sm" variant="outline" onClick={handleCancel}>
                  Cancel
                </Button>
              </TableCell>
            </TableRow>
          )}
          {filteredPatterns.map((pattern) =>
            editingId === pattern.id ? (
              <TableRow key={pattern.id}>
                <TableCell className="px-2 py-0">
                  <Input
                    value={editedPattern?.residentId ?? ""}
                    onChange={(e) =>
                      setEditedPattern({
                        ...editedPattern!,
                        residentId: e.target.value,
                      })
                    }
                  />
                </TableCell>
                <TableCell className="px-2 py-0">
                  <Textarea
                    value={editedPattern?.patterns ?? ""}
                    onChange={(e) =>
                      setEditedPattern({
                        ...editedPattern!,
                        patterns: e.target.value,
                      })
                    }
                    rows={1}
                  />
                </TableCell>
                <TableCell className="text-right space-x-2 py-0">
                  <Button size="sm" onClick={() => handleSave(pattern.id)}>
                    Save
                  </Button>
                  <Button size="sm" variant="outline" onClick={handleCancel}>
                    Cancel
                  </Button>
                </TableCell>
              </TableRow>
            ) : (
              <TableRow key={pattern.id}>
                <TableCell className="px-2 py-0">{pattern.residentId}</TableCell>
                <TableCell className="px-2 py-0">
                  {pattern.patterns.join(", ")}
                </TableCell>
                <TableCell className="text-right px-2 py-0">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleEdit(pattern)}>
                        Modify
                      </DropdownMenuItem>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <DropdownMenuItem
                            onSelect={(e) => e.preventDefault()} // Prevent dropdown from closing
                            className="text-red-500"
                          >
                            Delete
                          </DropdownMenuItem>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>
                              Are you absolutely sure?
                            </AlertDialogTitle>
                            <AlertDialogDescription>
                              This action cannot be undone. This will permanently
                              delete your resident pattern.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDelete(pattern.id)}
                            >
                              Continue
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            )
          )}
        </TableBody>
      </Table>
    </div>
  );
}
