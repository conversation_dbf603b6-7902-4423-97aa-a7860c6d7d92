## 1. Database Schema
- [ ] 1.1 Add the `ResidentPattern` model to `prisma/schema.prisma`.
- [ ] 1.2 Add relation to `User` model.
- [ ] 1.3 Generate and apply the migration.

## 2. Backend
- [ ] 2.1 Create API endpoints or server actions for CRUD operations on `ResidentPattern`.
    - [ ] `createResidentPattern`
    - [ ] `getResidentPatterns`
    - [ ] `updateResidentPattern`
    - [ ] `deleteResidentPattern`
- [ ] 2.2 Refactor `findResidentByPattern` in `src/lib/google/deposits-data-process.ts` to fetch patterns from the database for the current user.
- [ ] 2.3 Create a seeding script to migrate the existing hardcoded `RESIDENT_PATTERNS` into the database for existing users if necessary.

## 3. Frontend
- [ ] 3.1 Create a new page/section in the UI for managing resident patterns.
- [ ] 3.2 Implement a form to create and edit a `ResidentPattern`.
- [ ] 3.3 Implement a table or list to display all `ResidentPattern`s for the user.
- [ ] 3.4 Add functionality to delete a `ResidentPattern`.

## 4. Testing
- [ ] 4.1 Write unit tests for the new API endpoints/server actions.
- [ ] 4.2 Write integration tests for the UI components.
