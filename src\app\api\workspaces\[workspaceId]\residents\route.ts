import { NextResponse } from "next/server";
import { prisma } from "@/lib/db";

export async function GET(
  req: Request,
  props: { params: Promise<{ workspaceId: string }> }
) {
  try {
    const { workspaceId } = await props.params;
    const url = new URL(req.url);
    const q = (url.searchParams.get("q") || "").trim();
    if (!q) return NextResponse.json({ results: [] });

    const residents = await prisma.resident.findMany({
      where: {
        workspaceId,
        OR: [
          { residentId: { contains: q } },
          { name: { contains: q } },
        ],
      },
      take: 123,
      select: { id: true, residentId: true, name: true, type: true, ownerResidentId: true },
    })

    console.log("residents", residents)

    const results = residents.map((r) => ({
      id: r.id,
      residentId: r.residentId,
      name: r.name,
      type: r.type,
      ownerResidentId: r.ownerResidentId,
    }))

    return NextResponse.json({ results })
  } catch (err: any) {
    console.error('resident search error', err)
    return NextResponse.json({ results: [] }, { status: 500 })
  }
}
