// Utility function for MCP tools retry
export async function getZapierMCPToolsWithRetry(
  mcpClient: any,
  retries = 0,
  maxRetries = 3,
  delayMs = 1000
) {
  try {
    // Get Zapier tools
    const zapierTools = await mcpClient.tools();    

    return {
      ...zapierTools,
    };
  } catch (error) {
    if (retries < maxRetries) {
      console.log(`Retry ${retries + 1}/${maxRetries} fetching MCP tools...`);
      // Wait with exponential backoff
      await new Promise(resolve => setTimeout(resolve, delayMs * Math.pow(2, retries)));
      return getZapierMCPToolsWithRetry(mcpClient, retries + 1, maxRetries, delayMs);
    }
    throw error;
  }
}