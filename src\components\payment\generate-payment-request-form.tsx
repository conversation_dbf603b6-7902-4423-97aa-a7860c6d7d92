import {
  type PaymentRequest,
  type Payment,
  type PaymentAccount,
} from "@/hooks/use-payment-requests";

export const generatePaymentRequestForm = (
  payment: Payment,
  request: PaymentRequest,
  remitterAcc: PaymentAccount | undefined
) => {
  const currentMonthDay = new Date().getDate();
  const currentYear = request.year;
  const currentMonth = request.month;

  const requestId = payment.sequenceId;
  const defaultRemitter = remitterAcc;

  return (
    <div
      key={payment.id}
      className="page-break bg-white"
      style={{
        width: "21cm",
        height: "29.7cm",
        padding: "0.5cm 0.5cm",
        boxSizing: "border-box",
        display: "flex",
        flexDirection: "column",
        fontFamily: "serif",
        fontSize: "16px",
        margin: "0 auto",
      }}
    >
      {/* Header */}
      <div className="text-center mt-2 mb-6">
        <h1
          className="text-3xl font-bold"
          style={{ letterSpacing: "0.3em", lineHeight: "1.5" }}
        >
          {request.name}管理委員會請款單
        </h1>
      </div>

      {/* Top row with number and date */}
      <div className="flex justify-between items-center mb-2 text-xl font-bold">
        <div>
          請款單編號：<span className="ml-2">{requestId}</span>
        </div>
        <div>
          請款日期：
          <span className="ml-2">
            {currentYear}年{currentMonth}月{currentMonthDay}日
          </span>
        </div>
      </div>

      {/* Form fields in boxes */}
      <div className="space-y-0 mb-1">
        {/* 請款廠商 */}
        <div className="border-2 border-black px-3 py-2 text-2xl font-bold">
          請款廠商：
          <span className="ml-4">
            {payment.payeeAccount
              ? payment.payeeAccount.accountName
              : payment.payee}
          </span>
        </div>

        {/* 請款金額 */}
        <div className="border-2 border-black border-t-0 px-3 py-2 text-2xl font-bold">
          請款金額：
          <span className="ml-4 text-black font-bold">
            {payment?.payeeAccount &&
            payment.payeeAccount.isActive &&              
            payment.remitterAccount?.bankCode !=
              payment.payeeAccount?.bankCode ? (
              <>
                NT$ {payment.amount.toLocaleString()}元{" "}
                <span className="ml-2 text-xl">(含匯款手續費30元)</span>
              </>
            ) : (
              <>
                NT$ {payment.amount.toLocaleString()}元{" "}
                <span className="ml-2 text-xl">({payment.paymentMethod})</span>
              </>
            )}
          </span>
        </div>

        {/* 請款項目 */}
        <div className="border-2 border-black border-t-0 px-3 py-2 text-2xl font-bold whitespace-pre-line">
          請款項目：<span className="ml-4">{payment.accountingSubject}</span>
        </div>

        {/* 付款方式 */}
        <div className="border-2 border-black border-t-0 px-3 pt-2 text-xl font-bold">
          <div className="flex flex-rows justify-start items-center">
            <div className="text-2xl">付款方式：</div>
            {payment.payeeAccount && payment.payeeAccount.isActive ? (
              <>
                <div className="ml-4">
                  匯款：
                  <span className="ml-2">
                    {payment.payeeAccount.bankName}
                    {payment.payeeAccount.bankName.includes("農會") ? (
                      ""
                    ) : (
                      <span className="text-sm"> 銀行</span>　　
                    )}{" "}
                    
                    {payment.payeeAccount.branchName}{" "}
                    {payment.payeeAccount.branchName.includes("分部") ? (
                      ""
                    ) : (
                      <span className="text-sm"> 分行</span>
                    )}
                  </span>
                  <span className="ml-6">
                    帳號：
                    <span className="font-mono ml-2">
                      {payment.payeeAccount
                        ? payment.payeeAccount.accountNumber
                        : ""}
                    </span>
                  </span>
                </div>
              </>
            ) : (
              <div className="ml-4 text-xl">現金支付</div>
            )}
          </div>
          <div className="flex flex-rows justify-start items-center my-1 gap-x-1">
            <div className="ml-24"></div>
            {payment.payeeAccount && payment.payeeAccount.isActive ? (
              <div className="ml-8 pl-1">
                戶名：
                <span className="ml-2">
                  {payment.payeeAccount ? payment.payeeAccount.accountName : ""}
                </span>
              </div>
            ) : (
              ""
            )}
          </div>
        </div>
      </div>

      <div>
        {/* Signature section */}
        <div className="border-2 border-black">
          <div className="grid grid-cols-4 text-center text-sm print:text-lg font-bold">
            <div className="border-r-2 border-black py-1 h-48 flex flex-col">
              <div className="border-b-2 border-black mb-8">主任委員</div>
              <div className="flex-1 flex items-end justify-center pb-2">
                {/* Placeholder for stamp */}
                <div className="w-16 h-24"></div>
              </div>
            </div>
            <div className="border-r-2 border-black py-1 h-48 flex flex-col">
              <div className="border-b-2 border-black mb-8">監察委員</div>
              <div className="flex-1 flex items-end justify-center pb-2">
                <div className="w-16 h-24"></div>
              </div>
            </div>
            <div className="border-r-2 border-black py-1 h-48 flex flex-col">
              <div className="border-b-2 border-black mb-8">財務委員</div>
              <div className="flex-1 flex items-end justify-center pb-2">
                <div className="w-16 h-24"></div>
              </div>
            </div>
            <div className="py-1 h-48 flex flex-col">
              <div className="border-b-2 border-black mb-8">承辦人</div>
              <div className="flex-1 flex items-end justify-center pb-2">
                <div className="w-16 h-24"> </div>
                <div className="w-20 h-18 border-2 border-red-500 rounded flex items-center justify-center text-sm text-red-600">
                  行政總幹事
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* Receipt section */}
        <div className="mt-6 flex-1 flex flex-col justify-end">
          <div className="text-center">
            <div
              className="text-3xl font-bold tracking-widest"
              style={{ letterSpacing: "2em", marginLeft: "2em" }}
            >
              收據黏貼處
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
